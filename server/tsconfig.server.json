{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "removeComments": true, "noEmitOnError": true, "sourceMap": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["standalone-enhanced-server-production.ts", "simple-production-server.ts"], "exclude": ["node_modules", "dist", "../src/**/*", "tests", "**/*.test.ts", "**/*.spec.ts", "test-*.ts", "enhanced-production-server.ts", "index.ts", "server.ts", "monitoring-api.ts", "production-server.ts", "standalone-enhanced-server.ts", "middleware/**/*", "routes/**/*"]}