# Investra AI Email Processing API - Production Environment
# Task 13.2: Configure Environment & Secrets - Enhanced Configuration

# ================================
# SERVER CONFIGURATION
# ================================
NODE_ENV=production
PORT=3001
LOG_LEVEL=info

# ================================
# EMAIL SERVER CONFIGURATION
# ================================
# IMAP Configuration for production email server
IMAP_HOST=mail.investra.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=PRODUCTION_EMAIL_PASSWORD_HERE
IMAP_SECURE=true
IMAP_TLS_REJECT_UNAUTHORIZED=true
IMAP_FOLDER=INBOX
IMAP_ENABLED=true

# Email processing intervals (in milliseconds)
IMAP_CHECK_INTERVAL=30000
IMAP_RECONNECT_INTERVAL=60000
IMAP_CONNECTION_TIMEOUT=10000
IMAP_POLL_INTERVAL=30000
IMAP_AUTO_RECONNECT=true
IMAP_MAX_RETRIES=3
IMAP_LOGGER=false

# ================================
# API INTEGRATION
# ================================
# Frontend API endpoints
API_BASE_URL=https://api.investra.com
FRONTEND_URL=https://app.investra.com

# External API keys (set in production)
SUPABASE_URL=https://ecbuwhpipphdssqjwgfm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.QMWhB6lpgO3YRGg5kGKz7347DZzRcDiQ6QLupznZi1E
SUPABASE_SERVICE_ROLE_KEY=PRODUCTION_SUPABASE_SERVICE_ROLE_KEY_HERE

# Alternative environment variable names (for compatibility)
VITE_SUPABASE_URL=https://ecbuwhpipphdssqjwgfm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.QMWhB6lpgO3YRGg5kGKz7347DZzRcDiQ6QLupznZi1E

# ================================
# MONITORING & ALERTING
# ================================
# Service monitoring configuration
MONITORING_ENABLED=true
MONITORING_INTERVAL=30000
MONITORING_MEMORY_THRESHOLD_MB=512
MONITORING_CPU_THRESHOLD_PERCENT=80
MONITORING_ERROR_THRESHOLD_PERCENT=10
MONITORING_MAX_RESTARTS=3
MONITORING_RESTART_DELAY_MS=5000

# Legacy monitoring settings (compatibility)
HEALTH_CHECK_INTERVAL=60000
MEMORY_THRESHOLD=80
CPU_THRESHOLD=80

# Webhook for alerts (Slack, Discord, etc.)
MONITORING_WEBHOOK_URL=PRODUCTION_MONITORING_WEBHOOK_URL_HERE
MONITORING_WEBHOOK_ENABLED=true

# ================================
# LOGGING CONFIGURATION
# ================================
# Log file locations
LOG_DIR=/var/log/investra
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=30
LOG_DATE_PATTERN=YYYY-MM-DD

# Legacy logging settings (compatibility)
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# ================================
# SECURITY CONFIGURATION
# ================================
# CORS origins for production
CORS_ORIGINS=http://10.0.0.89,http://10.0.0.89:80,https://investra.com,https://app.investra.com,https://www.investra.com
CORS_ORIGIN=http://10.0.0.89

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_ENABLED=true

# Legacy rate limiting (compatibility)
API_RATE_LIMIT=100
API_RATE_WINDOW=900000

# Request size limits
MAX_REQUEST_SIZE=50mb
MAX_JSON_SIZE=50mb

# ================================
# DATABASE CONFIGURATION
# ================================
# Production database connection
DATABASE_URL=PRODUCTION_DATABASE_URL_HERE
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=30000

# Email processing configuration
DEFAULT_PORTFOLIO_ID=default

# ================================
# SSL/TLS CONFIGURATION
# ================================
# SSL certificate paths (managed by Let's Encrypt)
SSL_CERT_PATH=/etc/letsencrypt/live/api.investra.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/api.investra.com/privkey.pem

# ================================
# PERFORMANCE TUNING
# ================================
# Node.js performance settings
UV_THREADPOOL_SIZE=16
NODE_OPTIONS=--max-old-space-size=2048

# Email processing performance
EMAIL_BATCH_SIZE=10
EMAIL_PROCESSING_TIMEOUT=30000
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY=5000

# ================================
# FEATURE FLAGS
# ================================
# Feature toggles for production rollout
FEATURE_EMAIL_PROCESSING=true
FEATURE_BATCH_PROCESSING=true
FEATURE_MANUAL_REVIEW_QUEUE=true
FEATURE_DUPLICATE_DETECTION=true
FEATURE_AUTO_CATEGORIZATION=true

# ================================
# HEALTH CHECKS
# ================================
# Health check endpoints
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000

# External health check services
EXTERNAL_HEALTH_CHECK_URL=https://hc-ping.com/PRODUCTION_HEALTH_CHECK_UUID_HERE

# ================================
# ERROR REPORTING
# ================================
# External error reporting (Sentry, Bugsnag, etc.)
ERROR_REPORTING_ENABLED=true
SENTRY_DSN=PRODUCTION_SENTRY_DSN_HERE
SENTRY_ENVIRONMENT=production

# ================================
# MAINTENANCE MODE
# ================================
# Maintenance mode configuration
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="Email processing temporarily unavailable. Please try again later."