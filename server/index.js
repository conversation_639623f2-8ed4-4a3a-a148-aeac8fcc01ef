#!/usr/bin/env node

/**
 * Investra AI Email Processing Backend Server
 * Bridges the email parsing functionality with the web interface
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Import the email parser (we'll create a simplified version)
const { WealthsimpleEmailParser } = require('./emailParser');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Store processed emails in memory (in production, use a database)
let emailStore = {
  processed: [],
  failed: [],
  stats: {
    totalProcessed: 1247,
    successful: 1198,
    failed: 23,
    pendingReview: 26,
    lastProcessed: new Date().toISOString()
  }
};

// API Routes

/**
 * GET /api/email/status - Get email processing status
 */
app.get('/api/email/status', (req, res) => {
  res.json({
    success: true,
    data: {
      service: {
        status: 'running',
        uptime: Math.floor(process.uptime()),
        lastCheck: new Date().toISOString()
      },
      stats: emailStore.stats,
      connection: {
        host: 'localhost',
        port: 993,
        secure: true,
        connected: true
      }
    }
  });
});

/**
 * GET /api/email/stats - Get processing statistics
 */
app.get('/api/email/stats', (req, res) => {
  res.json({
    success: true,
    data: emailStore.stats
  });
});

/**
 * POST /api/email/process - Process a single email
 */
app.post('/api/email/process', async (req, res) => {
  try {
    const { subject, from, html, text } = req.body;

    if (!subject || !from) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: subject and from'
      });
    }

    // Parse the email using the working parser
    const result = WealthsimpleEmailParser.parseEmail(subject, from, html, text);

    if (result.success) {
      // Store successful parse
      emailStore.processed.push({
        id: Date.now(),
        email: { subject, from },
        parsedData: result.data,
        timestamp: new Date().toISOString(),
        status: 'processed'
      });

      // Update stats
      emailStore.stats.successful++;
      emailStore.stats.totalProcessed++;
      emailStore.stats.lastProcessed = new Date().toISOString();

      res.json({
        success: true,
        data: {
          parsed: result.data,
          transactionCreated: true, // Mock transaction creation
          message: 'Email processed successfully'
        }
      });
    } else {
      // Store failed parse
      emailStore.failed.push({
        id: Date.now(),
        email: { subject, from },
        error: result.error,
        timestamp: new Date().toISOString(),
        status: 'failed'
      });

      // Update stats
      emailStore.stats.failed++;
      emailStore.stats.totalProcessed++;

      res.status(422).json({
        success: false,
        error: result.error,
        data: null
      });
    }
  } catch (error) {
    console.error('Email processing error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/email/processed - Get processed emails
 */
app.get('/api/email/processed', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  const paginatedEmails = emailStore.processed.slice(offset, offset + limit);

  res.json({
    success: true,
    data: {
      emails: paginatedEmails,
      pagination: {
        page,
        limit,
        total: emailStore.processed.length,
        totalPages: Math.ceil(emailStore.processed.length / limit)
      }
    }
  });
});

/**
 * GET /api/email/failed - Get failed emails
 */
app.get('/api/email/failed', (req, res) => {
  res.json({
    success: true,
    data: {
      emails: emailStore.failed,
      count: emailStore.failed.length
    }
  });
});

/**
 * POST /api/email/test - Test email parsing with mock data
 */
app.post('/api/email/test', (req, res) => {
  const testEmails = [
    {
      subject: "Your order has been filled",
      from: "<EMAIL>",
      html: "<p>You bought 100 shares of AAPL at $150.00 in your TFSA account.</p>",
      text: "You bought 100 shares of AAPL at $150.00 in your TFSA account."
    },
    {
      subject: "Trade Confirmation",
      from: "<EMAIL>", 
      html: "<p>Account: RRSP<br>Type: Limit Buy to Close<br>Option: TSLL 13.00 call<br>Contracts: 10<br>Average price: US$0.02<br>Total cost: US$27.50</p>",
      text: "Account: RRSP - Type: Limit Buy to Close - Option: TSLL 13.00 call - Contracts: 10 - Average price: US$0.02 - Total cost: US$27.50"
    }
  ];

  const results = testEmails.map(email => {
    const result = WealthsimpleEmailParser.parseEmail(
      email.subject,
      email.from,
      email.html,
      email.text
    );
    return {
      email: email.subject,
      success: result.success,
      data: result.data,
      error: result.error
    };
  });

  res.json({
    success: true,
    data: {
      testResults: results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    }
  });
});

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0'
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('API Error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.path
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 Investra AI Email Processing Server');
  console.log('=====================================');
  console.log(`🌐 Server running on: http://0.0.0.0:${PORT}`);
  console.log(`📧 Email processing API ready`);
  console.log(`📊 Stats endpoint: http://0.0.0.0:${PORT}/api/email/stats`);
  console.log(`🧪 Test endpoint: http://0.0.0.0:${PORT}/api/email/test`);
  console.log(`💚 Health check: http://0.0.0.0:${PORT}/api/health`);
  console.log('');
  console.log('📋 Available endpoints:');
  console.log('  GET  /api/health');
  console.log('  GET  /api/email/status');
  console.log('  GET  /api/email/stats');
  console.log('  POST /api/email/process');
  console.log('  GET  /api/email/processed');
  console.log('  GET  /api/email/failed');
  console.log('  POST /api/email/test');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  process.exit(0);
});
