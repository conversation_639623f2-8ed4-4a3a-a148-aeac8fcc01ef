# Investra AI Email Processing API - Systemd Service Configuration
# Task 13.3: Set up Process Management - Systemd Integration
# File: /etc/systemd/system/investra-email-api.service

[Unit]
Description=Investra AI Email Processing API Server
Documentation=https://docs.investra.com/email-processing
After=network.target nginx.service
Wants=network.target
Requires=network.target

[Service]
Type=forking
User=investra
Group=investra
WorkingDirectory=/opt/investra/email-api
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PM2_HOME=/home/<USER>/.pm2
ExecStart=/usr/bin/pm2 start ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 restart all
ExecStop=/usr/bin/pm2 delete all
Restart=on-failure
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30
PIDFile=/var/run/pm2-investra.pid

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ReadWritePaths=/opt/investra/email-api /var/log/investra /tmp
ProtectHome=yes
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
RestrictRealtime=yes
RestrictSUIDSGID=yes
LockPersonality=yes
MemoryDenyWriteExecute=no
SystemCallArchitectures=native

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768
LimitCORE=0
MemoryMax=2G
CPUQuota=200%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=investra-email-api

[Install]
WantedBy=multi-user.target