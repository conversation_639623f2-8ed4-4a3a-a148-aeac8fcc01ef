# Nginx Configuration for Investra Email Processing API
# Task 13.4: Configure Reverse Proxy & SSL
# Production-ready reverse proxy with SSL termination

upstream investra_email_api {
    server localhost:3001;
    keepalive 32;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;

# Security headers map
map $sent_http_content_type $security_headers {
    default "nosniff";
}

server {
    listen 80;
    server_name api.investra.com email-api.investra.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.investra.com email-api.investra.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.investra.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.investra.com/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS (ngx_http_headers_module is required)
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;
    
    # Logging
    access_log /var/log/nginx/investra-email-api.access.log;
    error_log /var/log/nginx/investra-email-api.error.log;
    
    # Basic settings
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting for API endpoints
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        limit_req_status 429;
        
        # Proxy settings
        proxy_pass http://investra_email_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Proxy timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # Cache control
        proxy_cache_bypass $http_upgrade;
        
        # Hide upstream headers
        proxy_hide_header X-Powered-By;
        
        # Add custom headers
        add_header X-API-Version "1.0" always;
        add_header X-Server-ID "email-api-01" always;
    }
    
    # Health check endpoint (no rate limiting)
    location /health {
        proxy_pass http://investra_email_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Quick timeouts for health checks
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
        
        # Cache health checks briefly
        add_header Cache-Control "public, max-age=60";
    }
    
    # Authentication endpoints with stricter rate limiting
    location ~ /api/(auth|login|token) {
        limit_req zone=auth_limit burst=5 nodelay;
        limit_req_status 429;
        
        proxy_pass http://investra_email_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Longer timeouts for auth
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Block common attack patterns
    location ~* \.(env|git|htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        return 404;
    }
    
    # Block user agents
    if ($http_user_agent ~* (bot|crawler|spider|scraper)) {
        return 403;
    }
    
    # Block suspicious requests
    location ~* (union|select|insert|drop|delete|update|script|alert|javascript) {
        deny all;
        return 403;
    }
    
    # Robots.txt
    location = /robots.txt {
        add_header Content-Type text/plain;
        return 200 "User-agent: *\nDisallow: /\n";
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        return 204;
    }
    
    # Default location
    location / {
        proxy_pass http://investra_email_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Standard timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# Monitoring server (internal access only)
server {
    listen 127.0.0.1:8080;
    server_name localhost;
    
    # Nginx status
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
    
    # Custom monitoring endpoint
    location /monitoring {
        proxy_pass http://investra_email_api/api/monitoring;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        deny all;
    }
}