{"name": "investra-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:debug": "vite --mode development --debug", "start": "vite", "build": "tsc -b && vite build", "build:prod": "vite build", "build:staging": "tsc -b && vite build --mode staging", "build:debug": "tsc -b && vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "debug": "npm run dev:debug", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:unit": "vitest run src/**/*.test.ts", "test:integration": "vitest run src/test/integration/", "test:integration:watch": "vitest src/test/integration/", "test:e2e": "playwright test", "test:e2e:ci": "CI=true playwright test --workers=1 --retries=1", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:run && npm run test:e2e"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@peculiar/webcrypto": "^1.5.0", "@supabase/supabase-js": "^2.49.9", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "axios": "^1.9.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "imapflow": "^1.0.188", "lucide-react": "^0.511.0", "mailparser": "^3.7.3", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.1", "styled-components": "^6.1.18", "yahoo-finance2": "^2.13.3", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.53.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "happy-dom": "^15.10.6", "jsdom": "^26.0.0", "msw": "^2.7.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}}