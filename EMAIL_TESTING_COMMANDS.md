
# EMAIL CONFIGURATION SYSTEM - QUICK TEST

## 1. Open Application
open http://localhost:5173

## 2. Navigate to Email Configuration
# Email Management → Configuration

## 3. Test Gmail Configuration
# Select Gmail preset
# Enter: <EMAIL>
# Enter: [Gmail App Password]
# Click: Test Connection

## 4. Verify Success
# Expected: "Configuration saved to database successfully!"
# Check: No console errors
# Test: Page refresh loads configuration

## 5. Next Steps
# Generate Gmail App Password if testing successful
# Enable real email processing
