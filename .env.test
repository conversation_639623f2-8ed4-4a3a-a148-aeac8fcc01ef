# Test Environment Variables for E2E Testing
VITE_APP_NAME=Stock Tracker
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=test

# Test mode flags
VITE_TEST_MODE=true
VITE_MOCK_DATA_MODE=true
VITE_AUTH_BYPASS=true

# Supabase Configuration (kept for fallback, but mocked in test mode)
VITE_SUPABASE_URL=https://ecbuwhpipphdssqjwgfm.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVjYnV3aHBpcHBoZHNzcWp3Z2ZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NzU4NjEsImV4cCI6MjA2NDQ1MTg2MX0.QMWhB6lpgO3YRGg5kGKz7347DZzRcDiQ6QLupznZi1E

# Yahoo Finance API Configuration
VITE_YAHOO_FINANCE_API_KEY=

# Test Settings
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug

# Cache Configuration (faster for tests)
VITE_CACHE_DURATION=5000
VITE_HISTORICAL_CACHE_DURATION=10000
