name: Deploy Investra AI to RHEL VM

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Clean workspace permissions
        run: |
          echo "🧹 Cleaning workspace permissions..."
          WORK_DIR="${GITHUB_WORKSPACE:-$(pwd)}"
          
          # Fix any permission issues that might prevent checkout cleanup
          if [ -d "$WORK_DIR" ]; then
            echo "📁 Fixing permissions for: $WORK_DIR"
            sudo chown -R $USER:$USER "$WORK_DIR" || true
            sudo chmod -R u+rwX "$WORK_DIR" || true
            
            # Special handling for dist directory which often causes issues
            if [ -d "$WORK_DIR/dist" ]; then
              echo "🎯 Fixing dist directory permissions..."
              sudo chown -R $USER:$USER "$WORK_DIR/dist" || true
              sudo chmod -R 755 "$WORK_DIR/dist" || true
            fi
          fi
          echo "✅ Workspace cleanup completed"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          set -e
          npm ci

      - name: Build application
        run: |
          set -e
          # Copy production environment variables
          cp .env.production .env
          npm run build:prod

      - name: Build enhanced email server
        run: |
          set -e
          echo "🔧 Building enhanced email server..."
          
          # Install server dependencies
          cd server
          npm install
          
          # Build the standalone enhanced server
          npm run build:enhanced-server
          
          echo "✅ Enhanced email server built successfully"

      - name: Pre-deployment system check
        run: |
          echo "🔍 System Information:"
          echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d= -f2 | tr -d '\"')"
          echo "User: $USER"
          echo "Current directory: $(pwd)"
          echo "Available space: $(df -h . | tail -1 | awk '{print $4}')"
          
          echo "🔍 Service Status:"
          if systemctl is-active --quiet nginx; then
            echo "✅ Nginx is running"
          else
            echo "⚠️  Nginx is not running"
          fi
          
          if systemctl is-active --quiet firewalld; then
            echo "🔥 Firewalld is active"
          else
            echo "ℹ️  Firewalld is not active"
          fi
          
          if command -v getenforce &> /dev/null; then
            echo "🔒 SELinux status: $(getenforce)"
          else
            echo "ℹ️  SELinux is not available"
          fi

      - name: Setup web server
        run: |
          # Install Nginx if not present
          if ! command -v nginx &> /dev/null; then
            echo "🔧 Installing Nginx..."
            if command -v dnf &> /dev/null; then
              sudo dnf install -y nginx
            elif command -v yum &> /dev/null; then
              sudo yum install -y nginx
            else
              echo "❌ Package manager not found. Please install Nginx manually."
              exit 1
            fi
          else
            echo "✅ Nginx is already installed"
          fi

          # Stop nginx first to clean up any existing configs
          sudo systemctl stop nginx

          # Backup and replace main nginx.conf to eliminate conflicts
          sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
          
          # Create a clean nginx.conf without default server blocks
          sudo tee /etc/nginx/nginx.conf > /dev/null << 'EOF'
          user nginx;
          worker_processes auto;
          error_log /var/log/nginx/error.log;
          pid /run/nginx.pid;
          
          include /usr/share/nginx/modules/*.conf;
          
          events {
              worker_connections 1024;
          }
          
          http {
              log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                              '$status $body_bytes_sent "$http_referer" '
                              '"$http_user_agent" "$http_x_forwarded_for"';
          
              access_log /var/log/nginx/access.log main;
          
              sendfile on;
              tcp_nopush on;
              tcp_nodelay on;
              keepalive_timeout 65;
              types_hash_max_size 4096;
          
              include /etc/nginx/mime.types;
              default_type application/octet-stream;
          
              include /etc/nginx/conf.d/*.conf;
          }
          EOF

          # Remove any default configurations
          sudo rm -f /etc/nginx/conf.d/default.conf
          sudo rm -rf /etc/nginx/sites-enabled/
          sudo rm -rf /etc/nginx/sites-available/

          # Create conf.d directory
          sudo mkdir -p /etc/nginx/conf.d

          # Start nginx
          sudo systemctl enable nginx
          sudo systemctl start nginx

          # Configure firewall for HTTP traffic (if firewalld is active)
          if systemctl is-active --quiet firewalld; then
            echo "🔥 Configuring firewall for HTTP traffic..."
            sudo firewall-cmd --permanent --add-service=http
            sudo firewall-cmd --reload
          fi

          # Check if SELinux is enabled and configure if necessary
          if command -v getenforce &> /dev/null && [ "$(getenforce)" != "Disabled" ]; then
            echo "🔒 Configuring SELinux for web server..."
            sudo setsebool -P httpd_can_network_connect 1
            sudo semanage fcontext -a -t httpd_exec_t "/var/www/investra-ai(/.*)?" 2>/dev/null || true
            sudo restorecon -R /var/www/investra-ai 2>/dev/null || true
          fi

      - name: Prepare deployment directories
        run: |
          echo "🔍 Checking build output..."
          if [ ! -d "dist" ]; then
            echo "❌ dist directory not found!"
            exit 1
          fi
          
          echo "📁 Build output contents:"
          ls -la dist/
          
          if [ ! -f "dist/index.html" ]; then
            echo "❌ index.html not found in dist directory!"
            exit 1
          fi
          
          echo "✅ Build output verified"
          
          sudo mkdir -p /var/www/investra-ai
          sudo chown $USER:$USER /var/www/investra-ai
          mkdir -p /tmp/investra-ai-deploy
          cp -r dist/* /tmp/investra-ai-deploy/
          
          echo "📁 Deployment staging contents:"
          ls -la /tmp/investra-ai-deploy/

      - name: Finalize deployment
        run: |
          # Detect web server user automatically
          WEB_USER=""
          if id nginx >/dev/null 2>&1; then
            WEB_USER="nginx"
          elif id www-data >/dev/null 2>&1; then
            WEB_USER="www-data"
          elif id apache >/dev/null 2>&1; then
            WEB_USER="apache"
          elif id httpd >/dev/null 2>&1; then
            WEB_USER="httpd"
          else
            echo "⚠️  No standard web server user found, using current user: $USER"
            WEB_USER="$USER"
          fi

          echo "🔍 Using web server user: $WEB_USER"

          # Backup current deployment if it exists
          if [ -d "/var/www/investra-ai/current" ]; then
            sudo mv /var/www/investra-ai/current /var/www/investra-ai/backup-$(date +%Y%m%d-%H%M%S)
          fi

          # Move new deployment to final location
          sudo mv /tmp/investra-ai-deploy /var/www/investra-ai/current
          sudo chown -R "$WEB_USER:$WEB_USER" /var/www/investra-ai/current
          sudo chmod -R 755 /var/www/investra-ai/current

          # Create/update Nginx configuration
          sudo tee /etc/nginx/conf.d/investra-ai.conf > /dev/null << 'EOF'
          server {
              listen 80 default_server;
              listen [::]:80 default_server;
              server_name _;

              root /var/www/investra-ai/current;
              index index.html;

              # Enable compression
              gzip on;
              gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

              # Security headers
              add_header X-Frame-Options "SAMEORIGIN" always;
              add_header X-Content-Type-Options "nosniff" always;
              add_header X-XSS-Protection "1; mode=block" always;
              add_header Referrer-Policy "strict-origin-when-cross-origin" always;

              # Health check endpoint - must come before catch-all location
              location = /health {
                  access_log off;
                  return 200 "healthy\n";
                  add_header Content-Type text/plain;
              }

              # Cache static assets
              location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  access_log off;
              }

              # Handle React Router (SPA routing) - this catches everything else
              location / {
                  try_files $uri $uri/ /index.html;
              }

              # Error pages
              error_page 404 /index.html;
              error_page 500 502 503 504 /index.html;

              # Detailed logging for debugging
              access_log /var/log/nginx/investra-ai-access.log;
              error_log /var/log/nginx/investra-ai-error.log;
          }
          EOF

          # Test Nginx configuration
          sudo nginx -t

          # Restart Nginx completely to ensure clean state
          if [ $? -eq 0 ]; then
              echo "✅ Nginx configuration is valid, restarting..."
              sudo systemctl restart nginx
              sleep 3
              echo "✅ Deployment successful! Nginx restarted."
          else
              echo "❌ Nginx configuration test failed!"
              exit 1
          fi

          # Clean up old backups (keep last 5)
          sudo find /var/www/investra-ai -name "backup-*" -type d | sort -r | tail -n +6 | xargs sudo rm -rf

          # Display deployment info
          echo "🚀 Investra AI deployed successfully!"
          echo "📁 Deployed to: /var/www/investra-ai/current"
          echo "🌐 Available at: http://$(hostname -I | awk '{print $1}')"
          echo "📊 Build size: $(du -sh /var/www/investra-ai/current)"

      - name: Deploy enhanced email server
        run: |
          echo "📧 Deploying enhanced email server..."
          
          # CRITICAL: Stop any manually started services first
          echo "🛑 Stopping any manually started email services..."
          sudo systemctl stop investra-email-server 2>/dev/null || true
          sudo pkill -f "standalone-enhanced-server-production.js" || true
          sudo pkill -f "node.*3001" || true
          
          # Debug: Check what files were built
          echo "📋 Checking built server files:"
          ls -la server/dist/ || echo "No dist directory found"
          ls -la server/package.json || echo "No package.json found"
          ls -la server/.env.production || echo "No .env.production found"
          
          # Create temporary staging directory in user space
          mkdir -p /tmp/investra-email-server-staging
          
          # Copy server files to staging directory first
          if [ -d "server/dist" ] && [ "$(ls -A server/dist)" ]; then
            cp -r server/dist/* /tmp/investra-email-server-staging/
            echo "✅ Server dist files copied to staging"
          else
            echo "❌ No server dist files to copy"
            exit 1
          fi
          
          if [ -f "server/package.json" ]; then
            cp server/package.json /tmp/investra-email-server-staging/
            echo "✅ package.json copied to staging"
          else
            echo "❌ server/package.json not found"
            exit 1
          fi
          
          if [ -f "server/.env.production" ]; then
            cp server/.env.production /tmp/investra-email-server-staging/.env
            echo "✅ .env.production copied to staging"
          else
            echo "❌ server/.env.production not found"
            exit 1
          fi
          
          # Install production dependencies in staging (as user)
          cd /tmp/investra-email-server-staging
          npm install --omit=dev
          echo "✅ Node modules installed in staging"
          
          # Now create the final directory and move everything
          sudo mkdir -p /opt/investra-email-server
          
          # Remove any existing content
          sudo rm -rf /opt/investra-email-server/*
          
          # Move from staging to final location
          sudo cp -r /tmp/investra-email-server-staging/* /opt/investra-email-server/
          echo "✅ Server files moved to final location"
          
          # Create systemd service file with enhanced logging and port conflict handling
          sudo tee /etc/systemd/system/investra-email-server.service > /dev/null << 'EOF'
          [Unit]
          Description=Investra Enhanced Email API Server
          After=network.target
          
          [Service]
          Type=simple
          User=investra
          WorkingDirectory=/opt/investra-email-server
          Environment=NODE_ENV=production
          Environment=PORT=3001
          Environment=PATH=/usr/bin:/usr/local/bin
          ExecStart=/usr/bin/node standalone-enhanced-server-production.js
          ExecStop=/bin/kill -TERM $MAINPID
          ExecStopPost=/bin/bash -c 'fuser -k 3001/tcp || true'
          Restart=on-failure
          RestartSec=15
          KillMode=mixed
          KillSignal=SIGTERM
          TimeoutStopSec=30
          StandardOutput=journal
          StandardError=journal
          SyslogIdentifier=investra-email-server
          
          [Install]
          WantedBy=multi-user.target
          EOF
          
          # Create investra user if it doesn't exist
          if ! id investra >/dev/null 2>&1; then
            sudo useradd -r -s /bin/false investra
          fi
          
          # Set proper ownership and permissions for the service
          sudo chown -R investra:investra /opt/investra-email-server
          sudo chmod -R 755 /opt/investra-email-server
          sudo chmod 644 /opt/investra-email-server/.env
          sudo chmod +x /opt/investra-email-server/*.js
          sudo chmod -R 755 /opt/investra-email-server/node_modules
          
          # Clean up staging directory
          rm -rf /tmp/investra-email-server-staging
          echo "✅ Staging directory cleaned up"
          
          # Verify installation before starting service
          echo "🔍 Verifying server installation..."
          echo "📋 Final directory contents:"
          sudo ls -la /opt/investra-email-server/
          
          echo "📋 Checking main server file:"
          if [ -f "/opt/investra-email-server/standalone-enhanced-server-production.js" ]; then
            echo "✅ Main server file exists"
            echo "File size: $(stat -f%z /opt/investra-email-server/standalone-enhanced-server-production.js 2>/dev/null || stat -c%s /opt/investra-email-server/standalone-enhanced-server-production.js)"
          else
            echo "❌ Main server file missing!"
            exit 1
          fi
          
          echo "📋 Checking environment file:"
          if [ -f "/opt/investra-email-server/.env" ]; then
            echo "✅ Environment file exists"
            echo "First few lines (sanitized):"
            sudo head -5 /opt/investra-email-server/.env | sed 's/=.*/=***/'
          else
            echo "❌ Environment file missing!"
            exit 1
          fi
          
          echo "📋 Checking node_modules:"
          if [ -d "/opt/investra-email-server/node_modules" ]; then
            echo "✅ Node modules directory exists"
            echo "Module count: $(sudo find /opt/investra-email-server/node_modules -maxdepth 1 -type d | wc -l)"
          else
            echo "❌ Node modules missing!"
            exit 1
          fi
          
          echo "📋 Testing Node.js execution:"
          sudo -u investra /usr/bin/node --version || {
            echo "❌ Node.js execution failed for investra user"
            echo "Trying alternative node paths..."
            sudo -u investra which node || echo "Node not in PATH for investra user"
            sudo -u investra /usr/local/bin/node --version 2>/dev/null || echo "Node not at /usr/local/bin/node"
          }
          
          echo "📋 Testing server file execution directly:"
          cd /opt/investra-email-server
          echo "Current directory: $(pwd)"
          echo "Testing syntax check..."
          sudo -u investra /usr/bin/node -c standalone-enhanced-server-production.js 2>&1 || echo "❌ Syntax check failed"
          
          echo "📋 Testing direct execution with full error capture..."
          echo "Running server for 15 seconds to capture startup errors..."
          # Capture both stdout and stderr, run in background, then wait and kill
          sudo -u investra timeout 15s /usr/bin/node standalone-enhanced-server-production.js > /tmp/server-test.log 2>&1 &
          SERVER_PID=$!
          sleep 10
          
          # Check if process is still running (good sign)
          if kill -0 $SERVER_PID 2>/dev/null; then
            echo "✅ Server process is running successfully"
            kill $SERVER_PID 2>/dev/null || true
            wait $SERVER_PID 2>/dev/null || true
          else
            echo "❌ Server process exited early"
          fi
          
          echo "📋 Server test output:"
          cat /tmp/server-test.log 2>/dev/null || echo "No test log generated"
          rm -f /tmp/server-test.log
          
          echo "📋 Checking required environment variables:"
          sudo -u investra grep -E '^[A-Z_]+=' /opt/investra-email-server/.env | head -10 | sed 's/=.*/=***/' || echo "Failed to read env vars"
          
          # Reload systemd and start service
          sudo systemctl daemon-reload
          sudo systemctl enable investra-email-server
          
          # Stop any existing service first and clean up port conflicts
          echo "🔍 Stopping any existing email server services..."
          sudo systemctl stop investra-email-server 2>/dev/null || true
          
          # Wait for service to fully stop
          sleep 3
          
          # Check for and stop any PM2 processes that might conflict
          echo "🔍 Checking for PM2 conflicts on port 3001..."
          if command -v pm2 >/dev/null 2>&1; then
            echo "📋 Current PM2 processes:"
            pm2 list || echo "No PM2 processes found"
            
            # Stop and delete any processes using port 3001
            if netstat -tlnp 2>/dev/null | grep :3001 | grep -q PM2; then
              echo "⚠️ PM2 is using port 3001, stopping conflicting processes..."
              pm2 stop all || true
              pm2 delete all || true
              pm2 kill || true
              sleep 3
            fi
          fi
          
          # Kill any remaining PM2 processes
          echo "🔧 Killing any remaining PM2 processes..."
          sudo pkill -f PM2 || true
          sudo pkill -f "node.*pm2" || true
          
          # Comprehensive port cleanup - check multiple times and use different methods
          echo "🔍 Comprehensive port 3001 cleanup..."
          for attempt in {1..5}; do
            echo "Port cleanup attempt $attempt/5:"
            
            # Check what's using port 3001
            PORT_USERS=$(netstat -tlnp 2>/dev/null | grep :3001 || ss -tlnp 2>/dev/null | grep :3001 || echo "")
            
            if [ -z "$PORT_USERS" ]; then
              echo "✅ Port 3001 is free"
              break
            fi
            
            echo "⚠️ Port 3001 still in use: $PORT_USERS"
            
            # Extract PIDs and kill them
            BLOCKING_PIDS=$(echo "$PORT_USERS" | awk '{print $7}' | cut -d'/' -f1 | grep -v '-' | sort -u)
            
            if [ ! -z "$BLOCKING_PIDS" ]; then
              for PID in $BLOCKING_PIDS; do
                if [ "$PID" != "" ] && [ "$PID" != "-" ]; then
                  echo "🔧 Killing process $PID using port 3001..."
                  sudo kill -TERM $PID 2>/dev/null || true
                  sleep 1
                  # Force kill if still running
                  if kill -0 $PID 2>/dev/null; then
                    echo "🔧 Force killing process $PID..."
                    sudo kill -KILL $PID 2>/dev/null || true
                  fi
                fi
              done
            fi
            
            # Use fuser as backup method
            sudo fuser -k 3001/tcp 2>/dev/null || true
            
            sleep 2
          done
          
          # Final verification that port is free
          if netstat -tlnp 2>/dev/null | grep :3001 || ss -tlnp 2>/dev/null | grep :3001; then
            echo "❌ Failed to free port 3001 after all attempts"
            echo "📋 Processes still using port 3001:"
            netstat -tlnp 2>/dev/null | grep :3001 || ss -tlnp 2>/dev/null | grep :3001
            echo "📋 All processes containing 'node':"
            ps aux | grep node | grep -v grep
            exit 1
          else
            echo "✅ Port 3001 is now free and ready for deployment"
          fi
          
          sleep 2
          
          # Start the service
          echo "🚀 Starting enhanced email server service..."
          sudo systemctl start investra-email-server
          
          # Wait longer and check status multiple times
          echo "⏳ Waiting for service to stabilize..."
          for i in {1..6}; do
            sleep 5
            echo "Check $i/6:"
            if sudo systemctl is-active --quiet investra-email-server; then
              echo "✅ Service is active on check $i"
              break
            else
              echo "⚠️ Service not active yet on check $i"
              sudo systemctl status investra-email-server --no-pager -l | head -10
            fi
            
            if [ $i -eq 6 ]; then
              echo "❌ Service failed to start after 30 seconds"
              break
            fi
          done
          
          # Check service status with detailed debugging
          if sudo systemctl is-active --quiet investra-email-server; then
            echo "✅ Enhanced email server started successfully"
            
            # Additional checks for service health
            echo "📋 Checking if service is listening on port 3001..."
            if netstat -tlnp 2>/dev/null | grep :3001 || ss -tlnp 2>/dev/null | grep :3001; then
              echo "✅ Service is listening on port 3001"
            else
              echo "⚠️ Service not listening on port 3001 yet, checking again in 5 seconds..."
              sleep 5
              netstat -tlnp 2>/dev/null | grep :3001 || ss -tlnp 2>/dev/null | grep :3001 || echo "❌ Still not listening on port 3001"
            fi
            
            echo "📋 Final service status:"
            sudo systemctl status investra-email-server --no-pager -l
          else
            echo "❌ Enhanced email server failed to start"
            echo "📋 Service status:"
            sudo systemctl status investra-email-server --no-pager
            echo "📋 Recent logs:"
            sudo journalctl -u investra-email-server --no-pager -n 50
            echo "📋 Full service output since last restart:"
            sudo journalctl -u investra-email-server --no-pager --since "5 minutes ago"
            echo "📋 Directory contents:"
            sudo ls -la /opt/investra-email-server/
            echo "📋 Environment file:"
            sudo head -10 /opt/investra-email-server/.env 2>/dev/null || echo "No .env file found"
            echo "📋 Node version and path:"
            /usr/bin/node --version
            which node
            echo "📋 Testing file execution directly:"
            cd /opt/investra-email-server
            sudo -u investra /usr/bin/node --version 2>&1 || echo "Node execution failed for investra user"
            echo "📋 Testing server file syntax:"
            sudo -u investra /usr/bin/node -c standalone-enhanced-server-production.js 2>&1 || echo "Syntax check failed"
            echo "📋 Checking if server file has required dependencies:"
            head -20 /opt/investra-email-server/standalone-enhanced-server-production.js
            echo "📋 Checking package.json for main entry:"
            cat /opt/investra-email-server/package.json | grep -A5 -B5 "main\|scripts"
            exit 1
          fi
          
          # Configure firewall for email server port
          if systemctl is-active --quiet firewalld; then
            sudo firewall-cmd --permanent --add-port=3001/tcp
            sudo firewall-cmd --reload
          fi

      - name: Verify deployment
        run: |
          # Wait a moment for Nginx to fully reload
          sleep 5
          
          # Get the server IP
          SERVER_IP=$(hostname -I | awk '{print $1}')
          
          echo "🔍 Debug information:"
          echo "Server IP: $SERVER_IP"
          echo "Nginx configuration test:"
          sudo nginx -t
          
          echo "🔍 Active Nginx configurations:"
          sudo ls -la /etc/nginx/conf.d/
          
          echo "🔍 Application files:"
          ls -la /var/www/investra-ai/current/ | head -10
          
          echo "🔍 Testing Nginx response directly:"
          sudo systemctl status nginx --no-pager -l
          
          # Test health endpoint with more verbose output
          echo "🔍 Testing health endpoint..."
          HEALTH_RESPONSE=$(curl -v -f -s "http://$SERVER_IP/health" 2>&1 || echo "FAILED")
          if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
            echo "✅ Health check passed"
            echo "Response: $HEALTH_RESPONSE"
          else
            echo "⚠️  Health check failed"
            echo "Response: $HEALTH_RESPONSE"
            echo "📋 Detailed Nginx error logs:"
            sudo tail -20 /var/log/nginx/error.log
            echo "📋 Investra AI specific error logs:"
            sudo tail -10 /var/log/nginx/investra-ai-error.log 2>/dev/null || echo "No specific error log found"
          fi
          
          # Test main application with more verbose output
          echo "🔍 Testing main application..."
          APP_RESPONSE=$(curl -v -f -s "http://$SERVER_IP/" 2>&1 || echo "FAILED")
          if echo "$APP_RESPONSE" | grep -qi "investra\|html\|<!DOCTYPE"; then
            echo "✅ Application is responding correctly"
          else
            echo "❌ Application test failed!"
            echo "Response preview: $(echo "$APP_RESPONSE" | head -5)"
            echo "📋 Nginx status:"
            sudo systemctl status nginx --no-pager -l
            echo "📋 Nginx access logs:"
            sudo tail -10 /var/log/nginx/investra-ai-access.log 2>/dev/null || sudo tail -10 /var/log/nginx/access.log
            echo "📋 Nginx error logs:"
            sudo tail -20 /var/log/nginx/error.log
            echo "📋 Directory permissions:"
            sudo ls -la /var/www/investra-ai/current/
            echo "📋 File ownership:"
            sudo ls -la /var/www/investra-ai/current/index.html 2>/dev/null || echo "index.html not found"
            exit 1
          fi
          
          # Test enhanced email server
          echo "🔍 Testing enhanced email server..."
          EMAIL_SERVER_RESPONSE=$(curl -f -s "http://$SERVER_IP:3001/health" 2>&1 || echo "FAILED")
          if echo "$EMAIL_SERVER_RESPONSE" | grep -q "healthy"; then
            echo "✅ Enhanced email server is responding correctly"
            echo "Email server response: $EMAIL_SERVER_RESPONSE"
          else
            echo "❌ Enhanced email server test failed!"
            echo "Response: $EMAIL_SERVER_RESPONSE"
            echo "📋 Email server status:"
            sudo systemctl status investra-email-server --no-pager
            echo "📋 Email server logs:"
            sudo journalctl -u investra-email-server --no-pager -n 20
          fi
          
          echo "🎉 Deployment verification completed successfully!"
          echo "🌐 Frontend: http://$SERVER_IP"
          echo "📧 Email API: http://$SERVER_IP:3001"
