name: Deploy Investra AI to Staging Environment

on:
  push:
    branches: [ dev, develop, staging, 'development/**' ]
  pull_request:
    branches: [ main ]
    types: [ opened, synchronize, reopened ]

jobs:
  deploy-staging:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          set -e
          npm ci

      - name: Build application for staging
        run: |
          set -e
          # Copy staging environment variables (create .env.staging if it doesn't exist)
          if [ -f ".env.staging" ]; then
            cp .env.staging .env
          elif [ -f ".env.development" ]; then
            cp .env.development .env
          else
            echo "⚠️  No staging environment file found, using production"
            cp .env.production .env
          fi
          # Increase Node.js memory limit for large builds
          export NODE_OPTIONS="--max-old-space-size=4096"
          npm run build

      - name: Pre-deployment system check
        run: |
          echo "🔍 Staging Environment System Information:"
          echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d= -f2 | tr -d '\"')"
          echo "User: $USER"
          echo "Current directory: $(pwd)"
          echo "Available space: $(df -h . | tail -1 | awk '{print $4}')"
          echo "Branch: ${GITHUB_REF#refs/heads/}"
          echo "Commit: ${GITHUB_SHA:0:8}"
          
          echo "🔍 Service Status:"
          if systemctl is-active --quiet nginx; then
            echo "✅ Nginx is running"
          else
            echo "⚠️  Nginx is not running"
          fi

      - name: Setup staging web server
        run: |
          # Ensure Nginx is installed and running
          if ! command -v nginx &> /dev/null; then
            echo "🔧 Installing Nginx for staging..."
            if command -v dnf &> /dev/null; then
              sudo dnf install -y nginx
            elif command -v yum &> /dev/null; then
              sudo yum install -y nginx
            else
              echo "❌ Package manager not found. Please install Nginx manually."
              exit 1
            fi
          fi

          # Ensure nginx is enabled and started
          sudo systemctl enable nginx
          sudo systemctl start nginx

          # Configure firewall for staging (if firewalld is active)
          if systemctl is-active --quiet firewalld; then
            echo "🔥 Configuring firewall for staging environment..."
            sudo firewall-cmd --permanent --add-port=8080/tcp
            sudo firewall-cmd --reload
          fi

      - name: Prepare staging deployment directories
        run: |
          echo "🔍 Checking build output..."
          if [ ! -d "dist" ]; then
            echo "❌ dist directory not found!"
            exit 1
          fi
          
          echo "📁 Build output contents:"
          ls -la dist/
          
          if [ ! -f "dist/index.html" ]; then
            echo "❌ index.html not found in dist directory!"
            exit 1
          fi
          
          echo "✅ Build output verified"
          
          # Create staging directory structure
          sudo mkdir -p /var/www/investra-ai-staging
          sudo chown $USER:$USER /var/www/investra-ai-staging
          mkdir -p /tmp/investra-ai-staging-deploy
          cp -r dist/* /tmp/investra-ai-staging-deploy/
          
          echo "📁 Staging deployment contents:"
          ls -la /tmp/investra-ai-staging-deploy/

      - name: Deploy to staging
        run: |
          # Detect web server user automatically
          WEB_USER=""
          if id nginx >/dev/null 2>&1; then
            WEB_USER="nginx"
          elif id www-data >/dev/null 2>&1; then
            WEB_USER="www-data"
          elif id apache >/dev/null 2>&1; then
            WEB_USER="apache"
          elif id httpd >/dev/null 2>&1; then
            WEB_USER="httpd"
          else
            echo "⚠️  No standard web server user found, using current user: $USER"
            WEB_USER="$USER"
          fi

          echo "🔍 Using web server user: $WEB_USER"

          # Backup current staging deployment if it exists
          if [ -d "/var/www/investra-ai-staging/current" ]; then
            sudo mv /var/www/investra-ai-staging/current /var/www/investra-ai-staging/backup-$(date +%Y%m%d-%H%M%S)
          fi

          # Move new deployment to staging location
          sudo mv /tmp/investra-ai-staging-deploy /var/www/investra-ai-staging/current
          sudo chown -R "$WEB_USER:$WEB_USER" /var/www/investra-ai-staging/current
          sudo chmod -R 755 /var/www/investra-ai-staging/current

          # Create/update Nginx configuration for staging
          sudo tee /etc/nginx/conf.d/investra-ai-staging.conf > /dev/null << 'EOF'
          server {
              listen 8080;
              listen [::]:8080;
              server_name _;

              root /var/www/investra-ai-staging/current;
              index index.html;

              # Enable compression
              gzip on;
              gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

              # Security headers for staging
              add_header X-Frame-Options "SAMEORIGIN" always;
              add_header X-Content-Type-Options "nosniff" always;
              add_header X-XSS-Protection "1; mode=block" always;
              add_header Referrer-Policy "strict-origin-when-cross-origin" always;
              
              # Staging environment header
              add_header X-Environment "staging" always;

              # Health check endpoint for staging
              location = /health {
                  access_log off;
                  return 200 "staging-healthy\n";
                  add_header Content-Type text/plain;
              }

              # Cache static assets (shorter cache for staging)
              location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                  expires 1h;
                  add_header Cache-Control "public";
                  access_log off;
              }

              # Handle React Router (SPA routing)
              location / {
                  try_files $uri $uri/ /index.html;
              }

              # Error pages
              error_page 404 /index.html;
              error_page 500 502 503 504 /index.html;

              # Detailed logging for staging debugging
              access_log /var/log/nginx/investra-ai-staging-access.log;
              error_log /var/log/nginx/investra-ai-staging-error.log;
          }
          EOF

          # Test Nginx configuration
          sudo nginx -t

          # Reload Nginx to apply staging configuration
          if [ $? -eq 0 ]; then
              echo "✅ Nginx configuration is valid, reloading..."
              sudo systemctl reload nginx
              sleep 3
              echo "✅ Staging deployment successful!"
          else
              echo "❌ Nginx configuration test failed!"
              exit 1
          fi

          # Clean up old staging backups (keep last 3)
          sudo find /var/www/investra-ai-staging -name "backup-*" -type d | sort -r | tail -n +4 | xargs sudo rm -rf

          # Display staging deployment info
          echo "🚀 Investra AI deployed to staging successfully!"
          echo "📁 Deployed to: /var/www/investra-ai-staging/current"
          echo "🌐 Available at: http://$(hostname -I | awk '{print $1}'):8080"
          echo "📊 Build size: $(du -sh /var/www/investra-ai-staging/current)"
          echo "🔀 Branch: ${GITHUB_REF#refs/heads/}"
          echo "📝 Commit: ${GITHUB_SHA:0:8}"

      - name: Verify staging deployment
        run: |
          # Wait for Nginx to fully reload
          sleep 5
          
          # Get the server IP
          SERVER_IP=$(hostname -I | awk '{print $1}')
          
          echo "🔍 Staging deployment verification:"
          echo "Server IP: $SERVER_IP"
          echo "Port: 8080"
          echo "Branch: ${GITHUB_REF#refs/heads/}"
          
          # Test staging health endpoint
          echo "🔍 Testing staging health endpoint..."
          HEALTH_RESPONSE=$(curl -f -s "http://$SERVER_IP:8080/health" 2>/dev/null || echo "FAILED")
          if echo "$HEALTH_RESPONSE" | grep -q "staging-healthy"; then
            echo "✅ Staging health check passed"
          else
            echo "⚠️  Staging health check failed"
            echo "Response: $HEALTH_RESPONSE"
            echo "📋 Staging error logs:"
            sudo tail -10 /var/log/nginx/investra-ai-staging-error.log 2>/dev/null || echo "No staging error log found"
          fi
          
          # Test staging application
          echo "🔍 Testing staging application..."
          APP_RESPONSE=$(curl -f -s "http://$SERVER_IP:8080/" 2>/dev/null || echo "FAILED")
          if echo "$APP_RESPONSE" | grep -qi "investra\|html\|<!DOCTYPE"; then
            echo "✅ Staging application is responding correctly"
            echo "🎉 Staging deployment verification completed successfully!"
            echo ""
            echo "📍 Staging Environment Details:"
            echo "   URL: http://$SERVER_IP:8080"
            echo "   Branch: ${GITHUB_REF#refs/heads/}"
            echo "   Commit: ${GITHUB_SHA:0:8}"
            echo "   Environment: staging"
          else
            echo "❌ Staging application test failed!"
            echo "📋 Staging access logs:"
            sudo tail -10 /var/log/nginx/investra-ai-staging-access.log 2>/dev/null || echo "No staging access log found"
            echo "📋 Staging error logs:"
            sudo tail -10 /var/log/nginx/investra-ai-staging-error.log 2>/dev/null || echo "No staging error log found"
            exit 1
          fi

      - name: Post deployment notification
        if: always()
        run: |
          SERVER_IP=$(hostname -I | awk '{print $1}')
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ STAGING DEPLOYMENT SUCCESSFUL"
            echo "🌐 Access your staging environment at: http://$SERVER_IP:8080"
            echo "🔀 Branch: ${GITHUB_REF#refs/heads/}"
            echo "📝 Commit: ${GITHUB_SHA:0:8}"
          else
            echo "❌ STAGING DEPLOYMENT FAILED"
            echo "Check the logs above for details"
          fi
