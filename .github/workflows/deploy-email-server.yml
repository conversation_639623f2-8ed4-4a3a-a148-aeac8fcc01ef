name: Deploy Email Server (Self-Hosted)

on:
  push:
    branches: [ main, master, develop, staging ]
    paths:
      - 'email-server/**'
      - '.github/workflows/deploy-email-server.yml'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'email-server/**'
      - '.github/workflows/deploy-email-server.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

env:
  EMAIL_DOMAIN: investra.com
  EMAIL_HOSTNAME: mail.investra.com
  EMAIL_USER: <EMAIL>

jobs:
  determine-environment:
    name: Determine Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      hostname: ${{ steps.env.outputs.hostname }}
      email-user: ${{ steps.env.outputs.email-user }}
      email-password-secret: ${{ steps.env.outputs.email-password-secret }}
      admin-email-secret: ${{ steps.env.outputs.admin-email-secret }}
    
    steps:
    - name: Determine environment and configuration
      id: env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENVIRONMENT="${{ github.event.inputs.environment }}"
        elif [ "${{ github.event_name }}" = "pull_request" ]; then
          ENVIRONMENT="staging"
        else
          case "${{ github.ref_name }}" in
            main|master) ENVIRONMENT="production" ;;
            staging) ENVIRONMENT="staging" ;;
            develop|development) ENVIRONMENT="development" ;;
            *) ENVIRONMENT="development" ;;
          esac
        fi
        
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        
        case "$ENVIRONMENT" in
          production)
            echo "hostname=mail.investra.com" >> $GITHUB_OUTPUT
            echo "email-user=<EMAIL>" >> $GITHUB_OUTPUT
            echo "email-password-secret=EMAIL_PASSWORD" >> $GITHUB_OUTPUT
            echo "admin-email-secret=ADMIN_EMAIL" >> $GITHUB_OUTPUT
            ;;
          staging)
            echo "hostname=mail-staging.investra.com" >> $GITHUB_OUTPUT
            echo "email-user=<EMAIL>" >> $GITHUB_OUTPUT
            echo "email-password-secret=STAGING_EMAIL_PASSWORD" >> $GITHUB_OUTPUT
            echo "admin-email-secret=ADMIN_EMAIL" >> $GITHUB_OUTPUT
            ;;
          development)
            echo "hostname=mail-dev.investra.com" >> $GITHUB_OUTPUT
            echo "email-user=<EMAIL>" >> $GITHUB_OUTPUT
            echo "email-password-secret=DEV_EMAIL_PASSWORD" >> $GITHUB_OUTPUT
            echo "admin-email-secret=ADMIN_EMAIL" >> $GITHUB_OUTPUT
            ;;
        esac

  deploy-email-server:
    name: Deploy Email Server
    runs-on: self-hosted
    needs: determine-environment
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Debug environment
      run: |
        echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
        echo "Hostname: ${{ needs.determine-environment.outputs.hostname }}"
        echo "Email User: ${{ needs.determine-environment.outputs.email-user }}"
        echo "Current user: $(whoami)"
        echo "Server IP: $(hostname -I | awk '{print $1}')"

    - name: Verify secrets
      run: |
        EMAIL_PASSWORD="${{ secrets[needs.determine-environment.outputs.email-password-secret] }}"
        ADMIN_EMAIL="${{ secrets[needs.determine-environment.outputs.admin-email-secret] }}"
        
        if [ -z "$EMAIL_PASSWORD" ]; then
          echo "❌ Email password secret missing"
          exit 1
        fi
        
        if [ -z "$ADMIN_EMAIL" ]; then
          echo "❌ Admin email secret missing"
          exit 1
        fi
        
        echo "✅ All required secrets are available"

    - name: Install dependencies
      run: |
        echo "🔗 Installing dependencies"
        sudo dnf update -y
        
        if ! command -v podman &> /dev/null; then
          sudo dnf install -y podman podman-compose podman-docker
          systemctl --user enable podman.socket
          systemctl --user start podman.socket
        fi
        
        sudo loginctl enable-linger $USER
        podman --version

    - name: Configure firewall
      run: |
        echo "🔥 Configuring firewall"
        
        if sudo systemctl is-active --quiet firewalld; then
          sudo firewall-cmd --permanent --add-service=smtp
          sudo firewall-cmd --permanent --add-service=smtp-submission  
          sudo firewall-cmd --permanent --add-service=imaps
          sudo firewall-cmd --permanent --add-port=8080/tcp
          sudo firewall-cmd --reload
        fi
        
        if sudo systemctl is-active --quiet postfix; then
          sudo systemctl stop postfix
          sudo systemctl disable postfix
        fi

    - name: Setup email server
      run: |
        echo "📁 Setting up email server directory"
        mkdir -p ~/investra-email-server
        cp -r email-server/* ~/investra-email-server/
        cd ~/investra-email-server

    - name: Configure environment
      run: |
        cd ~/investra-email-server
        EMAIL_PASSWORD="${{ secrets[needs.determine-environment.outputs.email-password-secret] }}"
        SERVER_IP=$(hostname -I | awk '{print $1}')
        
        echo "HOSTNAME=${{ needs.determine-environment.outputs.hostname }}" > .env
        echo "DOMAINNAME=${{ env.EMAIL_DOMAIN }}" >> .env
        echo "MAILSERVER_USER=${{ needs.determine-environment.outputs.email-user }}" >> .env
        echo "MAILSERVER_PASS=$EMAIL_PASSWORD" >> .env
        echo "IMAP_HOST=$SERVER_IP" >> .env
        echo "SMTP_HOST=$SERVER_IP" >> .env
        echo "DMS_DEBUG=0" >> .env
        echo "LOG_LEVEL=warn" >> .env

    - name: Setup SSL certificates
      run: |
        cd ~/investra-email-server
        
        # Make SSL setup script executable
        chmod +x setup-ssl.sh
        
        # Run the improved SSL setup script in automated mode
        ADMIN_EMAIL="${{ secrets[needs.determine-environment.outputs.admin-email-secret] }}" \
        HOSTNAME="${{ needs.determine-environment.outputs.hostname }}" \
        EMAIL_DOMAIN="${{ env.EMAIL_DOMAIN }}" \
        ./setup-ssl.sh auto

    - name: Verify SSL certificates
      run: |
        cd ~/investra-email-server
        HOSTNAME="${{ needs.determine-environment.outputs.hostname }}"
        
        if [ -f "docker-data/dms/certs/$HOSTNAME/fullchain.pem" ] && [ -f "docker-data/dms/certs/$HOSTNAME/privkey.pem" ]; then
          echo "✅ SSL certificates are present"
          openssl x509 -in docker-data/dms/certs/$HOSTNAME/fullchain.pem -noout -subject -dates
        else
          echo "❌ SSL certificates are missing"
          echo "📋 Available certificate files:"
          ls -la docker-data/dms/certs/$HOSTNAME/ || echo "Certificate directory not found"
          exit 1
        fi

    - name: Setup email accounts
      run: |
        cd ~/investra-email-server
        EMAIL_PASSWORD="${{ secrets[needs.determine-environment.outputs.email-password-secret] }}"
        EMAIL_USER="${{ needs.determine-environment.outputs.email-user }}"
        
        mkdir -p docker-data/dms/config
        mkdir -p docker-data/dms/mail-data
        mkdir -p docker-data/dms/mail-state
        mkdir -p docker-data/dms/mail-logs
        
        echo "$EMAIL_USER|{PLAIN}$EMAIL_PASSWORD" > docker-data/dms/config/postfix-accounts.cf

    - name: Start email server
      run: |
        cd ~/investra-email-server
        
        # Make startup script executable
        chmod +x start-mailserver.sh
        
        # Run the improved startup script in automated mode
        EMAIL_PASSWORD="${{ secrets[needs.determine-environment.outputs.email-password-secret] }}" \
        HOSTNAME="${{ needs.determine-environment.outputs.hostname }}" \
        EMAIL_DOMAIN="${{ env.EMAIL_DOMAIN }}" \
        EMAIL_USER="${{ needs.determine-environment.outputs.email-user }}" \
        ./start-mailserver.sh auto

    - name: Verify email server
      run: |
        cd ~/investra-email-server
        
        # Additional verification after startup
        echo "🔍 Verifying email server deployment..."
        
        # Check container status
        podman ps --filter "name=mailserver"
        
        # Check logs for any errors
        echo ""
        echo "📋 Recent logs:"
        podman logs --tail 20 mailserver || echo "No logs available yet"
        
        # Run connectivity test
        echo ""
        echo "🧪 Running additional connectivity tests..."
        ./start-mailserver.sh test

    - name: Setup monitoring
      run: |
        cd ~/investra-email-server
        echo "📊 Setting up monitoring"
        
        cat > monitor-email.sh << 'SCRIPT_END'
        #!/bin/bash
        echo "=== Email Server Status $(date) ==="
        echo "Containers:"
        podman compose ps
        echo "Ports:"
        ss -tlnp | grep -E ':(25|587|993|8080)' || echo "No ports found"
        echo "Logs:"
        podman compose logs --tail=5 mailserver
        SCRIPT_END
        
        chmod +x monitor-email.sh

    - name: Create backup script  
      run: |
        cd ~/investra-email-server
        echo "💾 Setting up backup"
        
        cat > backup-email.sh << 'SCRIPT_END'
        #!/bin/bash
        BACKUP_DIR="/backup/investra-email"
        DATE=$(date +%Y%m%d_%H%M%S)
        mkdir -p $BACKUP_DIR
        tar -czf "$BACKUP_DIR/email-data-$DATE.tar.gz" docker-data/ .env docker-compose.yml
        find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
        echo "Backup completed: $BACKUP_DIR/email-data-$DATE.tar.gz"
        SCRIPT_END
        
        chmod +x backup-email.sh

    - name: Display summary
      run: |
        EMAIL_USER="${{ needs.determine-environment.outputs.email-user }}"
        HOSTNAME="${{ needs.determine-environment.outputs.hostname }}"
        SERVER_IP=$(hostname -I | awk '{print $1}')
        
        echo "============================================"
        echo "📧 EMAIL SERVER DEPLOYMENT COMPLETE"
        echo "============================================"
        echo "Server: $HOSTNAME"
        echo "IP: $SERVER_IP"
        echo "Email: $EMAIL_USER"
        echo "Web: http://$SERVER_IP:8080"
        echo "============================================"

    - name: Success notification
      if: success()
      run: |
        echo "✅ Email server deployed successfully"

    - name: Failure notification
      if: failure()
      run: |
        echo "❌ Deployment failed - check logs above"
