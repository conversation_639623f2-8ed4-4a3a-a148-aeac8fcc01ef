# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
playwright/.cache/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Added by Task Master AI
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.vscode
# OS specific
# Task files
tasks.json
tasks/

# E2E test auth files

# TypeScript compilation artifacts (should not be committed for frontend)
src/**/*.js
src/**/*.d.ts
src/**/*.js.map
src/**/*.d.ts.map
!src/**/*.test.js
!src/**/*.spec.js
!src/**/*.test.d.ts
!src/**/*.spec.d.ts

# Temporary files and scripts
*.tmp
*.temp
*-debug.js
*-debug.mjs
*-debug.html
*-test.js
*-test.mjs
debug-*.js
debug-*.mjs
test-*.js
test-*.mjs

# Database exports and backups
*-supabase-export-*.json
*.sql

# Shell scripts (unless in tools/ directory)
/*.sh

# Editor-specific directories
.cursor/
.roo/
.claude/
.windsurfrules
.roomodes

# E2E test auth files
src/test/e2e/.auth/ 