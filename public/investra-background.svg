<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <defs>
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700"/>
      <stop offset="50%" style="stop-color:#FFA500"/>
      <stop offset="100%" style="stop-color:#FF8C00"/>
    </linearGradient>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5F5DC"/>
      <stop offset="100%" style="stop-color:#E6E6D4"/>
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2F5233"/>
      <stop offset="100%" style="stop-color:#1a3d1f"/>
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#backgroundGradient)"/>
  
  <!-- Bottom hills representing growth -->
  <ellipse cx="600" cy="750" rx="500" ry="100" fill="#D3E6D3" opacity="0.8"/>
  <ellipse cx="300" cy="780" rx="350" ry="80" fill="#C8E1C8" opacity="0.6"/>
  <ellipse cx="900" cy="780" rx="300" ry="60" fill="#C8E1C8" opacity="0.6"/>
  
  <!-- Scattered coins throughout the background -->
  <g opacity="0.3">
    <circle cx="150" cy="200" r="25" fill="url(#coinGradient)" opacity="0.6"/>
    <text x="150" y="208" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#B8860B">$</text>
    
    <circle cx="800" cy="150" r="20" fill="url(#coinGradient)" opacity="0.5"/>
    <text x="800" y="157" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#B8860B">$</text>
    
    <circle cx="1000" cy="300" r="30" fill="url(#coinGradient)" opacity="0.4"/>
    <text x="1000" y="309" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#B8860B">$</text>
    
    <circle cx="200" cy="450" r="22" fill="url(#coinGradient)" opacity="0.5"/>
    <text x="200" y="458" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#B8860B">$</text>
  </g>
  
  <!-- Bar charts representing investment growth -->
  <g opacity="0.4">
    <g transform="translate(350,500)">
      <rect x="0" y="30" width="15" height="40" fill="url(#greenGradient)" rx="2"/>
      <rect x="20" y="20" width="15" height="50" fill="url(#greenGradient)" rx="2"/>
      <rect x="40" y="10" width="15" height="60" fill="url(#greenGradient)" rx="2"/>
      <rect x="60" y="0" width="15" height="70" fill="url(#greenGradient)" rx="2"/>
    </g>
    
    <g transform="translate(750,400)">
      <rect x="0" y="40" width="12" height="30" fill="url(#greenGradient)" rx="2"/>
      <rect x="16" y="25" width="12" height="45" fill="url(#greenGradient)" rx="2"/>
      <rect x="32" y="15" width="12" height="55" fill="url(#greenGradient)" rx="2"/>
      <rect x="48" y="5" width="12" height="65" fill="url(#greenGradient)" rx="2"/>
    </g>
  </g>
  
  <!-- Scattered leaves representing sustainable growth -->
  <g opacity="0.3">
    <ellipse cx="100" cy="350" rx="15" ry="25" fill="#2F5233" transform="rotate(-25 100 350)"/>
    <ellipse cx="500" cy="250" rx="12" ry="20" fill="#2F5233" transform="rotate(15 500 250)"/>
    <ellipse cx="900" cy="500" rx="18" ry="28" fill="#2F5233" transform="rotate(-10 900 500)"/>
    <ellipse cx="1100" cy="400" rx="14" ry="22" fill="#2F5233" transform="rotate(30 1100 400)"/>
    <ellipse cx="300" cy="150" rx="16" ry="24" fill="#2F5233" transform="rotate(-20 300 150)"/>
  </g>
  
  <!-- Subtle pattern overlay -->
  <pattern id="dots" patternUnits="userSpaceOnUse" width="50" height="50">
    <circle cx="25" cy="25" r="1" fill="#2F5233" opacity="0.1"/>
  </pattern>
  <rect width="1200" height="800" fill="url(#dots)"/>
</svg>
