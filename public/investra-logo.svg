<svg viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions for the golden theme -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FACC15;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#EAB308;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CA8A04;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="goldGradientDark" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#CA8A04;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A16207;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#854D0E;stop-opacity:1" />
    </linearGradient>

    <!-- Text gradient -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#854D0E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CA8A04;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo icon container with curved frame -->
  <g transform="translate(20,10)">
    <!-- Outer curved frame -->
    <path d="M 5 15 Q 5 5 15 5 L 65 5 Q 75 5 75 15 L 75 55 Q 75 65 65 65 L 15 65 Q 5 65 5 55 Z" 
          fill="none" 
          stroke="url(#goldGradient)" 
          stroke-width="2"/>
    
    <!-- Growth chart bars -->
    <rect x="15" y="40" width="8" height="15" fill="url(#goldGradient)" rx="2"/>
    <rect x="28" y="30" width="8" height="25" fill="url(#goldGradient)" rx="2"/>
    <rect x="41" y="20" width="8" height="35" fill="url(#goldGradient)" rx="2"/>
    
    <!-- Curved growth arrow/line -->
    <path d="M 12 50 Q 25 45 40 35 Q 55 25 70 15" 
          fill="none" 
          stroke="url(#goldGradientDark)" 
          stroke-width="2.5" 
          stroke-linecap="round"/>
    
    <!-- Arrow head -->
    <polygon points="67,12 73,15 67,18" fill="url(#goldGradientDark)"/>
    
    <!-- Decorative elements -->
    <circle cx="55" cy="25" r="2" fill="#FACC15" opacity="0.8"/>
    <circle cx="42" cy="35" r="1.5" fill="#EAB308" opacity="0.6"/>
  </g>
  
  <!-- Company name -->
  <text x="110" y="45" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="28" 
        font-weight="700" 
        fill="url(#textGradient)" 
        letter-spacing="0.5px">INVESTRA</text>
  
  <!-- Tagline -->
  <text x="112" y="62" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="11" 
        font-weight="500" 
        fill="#A16207" 
        letter-spacing="2px" 
        opacity="0.8">AI-POWERED INVESTING</text>
</svg>