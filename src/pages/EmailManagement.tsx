/**
 * Email Import Management Page
 * Unified interface for email review stats, manual review, error notifications, and configuration
 */
import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  Mail, 
  BarChart3,
  Eye,
  AlertTriangle,
  Settings,
  RefreshCw
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { usePageTitle } from '../hooks/usePageTitle';
import { useEmailProcessing } from '../hooks/useEmailProcessing';

// Import the consolidated email management components
import EmailReviewStats from '../components/EmailReviewStats';
import ManualEmailReview from '../components/ManualEmailReview';
import ImportStatusNotifications from '../components/ImportStatusNotifications';
import SimpleEmailConfiguration from '../components/SimpleEmailConfiguration';

const PageContainer = styled.div`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;
`;

const PageTitle = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;

  [data-theme="dark"] & {
    color: #f1f5f9;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;

  [data-theme="dark"] & {
    color: #94a3b8;
  }
`;

const SectionGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
`;

const Section = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  [data-theme="dark"] & {
    color: #f3f4f6;
  }
`;

const SectionCard = styled(Card)`
  padding: 1.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;

  [data-theme="dark"] & {
    background: #374151;
    border-color: #4b5563;
  }
`;

const QuickStatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  
  [data-theme="dark"] & {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #4b5563;
  }
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const StatTitle = styled.h3`
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  [data-theme="dark"] & {
    color: #9ca3af;
  }
`;

const StatIcon = styled.div<{ $color: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: ${props => props.$color};
  border-radius: 8px;
  color: white;
`;

const StatValue = styled.div`
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;

  [data-theme="dark"] & {
    color: #f3f4f6;
  }
`;

const StatDescription = styled.div`
  font-size: 0.875rem;
  color: #6b7280;

  [data-theme="dark"] & {
    color: #9ca3af;
  }
`;

const ActionBar = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
`;

const EmailManagementPage: React.FC = () => {
  // Set page title
  usePageTitle('Email Import Management', { subtitle: 'Unified Email Review & Configuration' });

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Email Import Management</PageTitle>
        <PageSubtitle>
          Unified interface for email review statistics, manual processing, error notifications, and configuration
        </PageSubtitle>
      </PageHeader>

      <SectionGrid>
        {/* Section 1: Email Review Statistics */}
        <Section>
          <SectionHeader>
            <SectionTitle>
              <BarChart3 size={24} />
              Email Review Statistics
            </SectionTitle>
          </SectionHeader>
          <SectionCard>
            <EmailReviewStats />
          </SectionCard>
        </Section>

        {/* Section 2: Manual Email Review */}
        <Section>
          <SectionHeader>
            <SectionTitle>
              <Eye size={24} />
              Manual Email Review
            </SectionTitle>
          </SectionHeader>
          <SectionCard>
            <ManualEmailReview />
          </SectionCard>
        </Section>

        {/* Section 3: Error Notifications */}
        <Section>
          <SectionHeader>
            <SectionTitle>
              <AlertTriangle size={24} />
              Error Notifications & Alerts
            </SectionTitle>
          </SectionHeader>
          <SectionCard>
            <ImportStatusNotifications />
          </SectionCard>
        </Section>

        {/* Section 4: Email Configuration */}
        <Section>
          <SectionHeader>
            <SectionTitle>
              <Settings size={24} />
              Email Configuration
            </SectionTitle>
          </SectionHeader>
          <SectionCard>
            <SimpleEmailConfiguration />
          </SectionCard>
        </Section>
      </SectionGrid>
    </PageContainer>
  );
};

export default EmailManagementPage;
