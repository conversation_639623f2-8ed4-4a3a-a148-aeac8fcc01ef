/* Modern Financial UI Design System - 2024-2025 Best Practices */
/* Investra AI - Premium Golden Theme */

@import './components.css';

:root {
  /* Primary Color: Logo Gold/Amber Colors */
  --color-primary-50: #faebd0;     /* Based on logo hex #faebd0 */
  --color-primary-100: #fbedd3;    /* Based on logo hex #fbedd3 */
  --color-primary-200: #fbedd1;    /* Based on logo hex #fbedd1 */
  --color-primary-300: #fbecd2;    /* Based on logo hex #fbecd2 */
  --color-primary-400: #fbedd2;    /* Based on logo hex #fbedd2 */
  --color-primary-500: #D97706;    /* Main gold color */
  --color-primary-600: #CA8A04;    /* Darker gold */
  --color-primary-700: #A16207;    /* Logo's darker gold */
  --color-primary-800: #854D0E;    /* Even darker gold */
  --color-primary-900: #713F12;    /* Darkest gold */

  /* New Secondary Color: Dark Blue-Gray (#4A5568) */
  --color-secondary-50: #eceff1;   /* Lightest Blue-Gray */
  --color-secondary-100: #cfd8dc;
  --color-secondary-200: #b0bec5;
  --color-secondary-300: #90a4ae;
  --color-secondary-400: #78909c;
  --color-secondary-500: #607d8b;   /* Base Blue-Gray */
  --color-secondary-600: #546e7a;
  --color-secondary-700: #4A5568;   /* Chosen Secondary */
  --color-secondary-800: #37474f;
  --color-secondary-900: #263238;   /* Darkest Blue-Gray */

  /* New Accent Color: Warm Gold/Amber (#F59E0B) */
  --color-accent-50: #fff8e1;    /* Lightest Amber */
  --color-accent-100: #ffecb3;
  --color-accent-200: #ffe082;
  --color-accent-300: #ffd54f;
  --color-accent-400: #ffca28;
  --color-accent-500: #ffc107;    /* Base Amber */
  --color-accent-600: #ffb300;
  --color-accent-700: #F59E0B;    /* Chosen Accent */
  --color-accent-800: #ffa000;
  --color-accent-900: #ff8f00;    /* Darkest Amber */

  /* Refined Neutral Grays (Inspired by Tailwind) */
  --color-gray-50: #F7FAFC;
  --color-gray-100: #EDF2F7;
  --color-gray-200: #E2E8F0;
  --color-gray-300: #CBD5E1;
  --color-gray-400: #A0AEC0;
  --color-gray-500: #718096;
  --color-gray-600: #4A5568;    /* Matches New Secondary */
  --color-gray-700: #2D3748;
  --color-gray-800: #1A202C;
  --color-gray-900: #171923;

  /* Success/Growth Colors (Keeping existing for now) */
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  
  /* Alert/Warning Colors (Orange instead of pure red for accessibility) */
  --color-warning-50: var(--color-accent-50);   /* Aligning with new Accent scale */
  --color-warning-100: var(--color-accent-100);
  --color-warning-500: var(--color-accent-700); /* #F59E0B */
  --color-warning-600: #dd8e0a; /* Darker shade of accent */
  --color-warning-700: #c07b09; /* Even darker shade */
  
  /* Critical/Danger Colors (Red for alerts only - Keeping existing) */
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  
  /* Light Theme Variables - Updated with new palette */
  --bg-primary: #ffffff;
  --bg-secondary: var(--color-gray-50);    /* #F7FAFC */
  --bg-tertiary: var(--color-gray-100);   /* #EDF2F7 */
  --bg-card: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.95); /* Keeping as is, can be refined later if needed */
  
  --text-primary: var(--color-gray-800);   /* #1A202C */
  --text-secondary: var(--color-gray-600); /* #4A5568 */
  --text-tertiary: var(--color-gray-500);  /* #718096 */
  --text-muted: var(--color-gray-400);    /* #A0AEC0 */
  --text-inverse: #ffffff;
  
  --border-primary: var(--color-gray-200); /* #E2E8F0 */
  --border-secondary: var(--color-gray-300);/* #CBD5E1 */
  --border-focus: var(--color-primary-600); /* Updated to match new gold color scheme */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* Keeping existing shadows */
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Typography - Updated with "Inter" */
  --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-family-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace; /* Keeping mono stack */
  --font-family-financial: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  
  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Typography scale with tabular figures for financial data */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  
  /* Line heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* Spacing scale */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 200ms ease;
  --transition-slow: 300ms ease;
  
  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  
  /* Form field heights for consistency */
  --input-height: 44px;
  --input-height-sm: 36px;
  --input-height-lg: 52px;
  
  /* Component specific */
  --navbar-height: 64px;
  --sidebar-width: 240px;
}

/* Dark Theme - Premium Dark Mode with Golden Accents */
[data-theme="dark"] {
  /* Dark backgrounds - sophisticated charcoal with warm undertones */
  --bg-primary: #1a1410;           /* Very dark warm charcoal */
  --bg-secondary: #2a211a;         /* Dark warm brown */
  --bg-tertiary: #3a2f26;          /* Medium warm brown */
  --bg-card: #241e18;              /* Dark card background */
  --bg-overlay: rgba(26, 20, 16, 0.95);
  
  /* Dark theme text with golden warmth */
  --text-primary: #FEF3C7;         /* Light gold - primary text */
  --text-secondary: #FDE047;       /* Brighter gold - secondary */
  --text-tertiary: #FACC15;        /* Medium gold - tertiary */
  --text-muted: #EAB308;           /* Darker gold - muted */
  --text-inverse: #1a1410;         /* Dark background color */
  
  /* Dark theme borders with golden highlights */
  --border-primary: #CA8A04;       /* Main brand gold */
  --border-secondary: #A16207;     /* Darker gold */
  --border-focus: var(--color-primary-500); /* Updated gold color for dark theme focus */
  
  /* Enhanced shadows for dark theme with warm tones */
  --shadow-sm: 0 1px 2px 0 rgba(202, 138, 4, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(202, 138, 4, 0.15), 0 2px 4px -1px rgba(202, 138, 4, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(202, 138, 4, 0.2), 0 4px 6px -2px rgba(202, 138, 4, 0.15);
  
  /* Adjust interactive colors for better dark theme visibility */
  /* Adjust interactive colors for better dark theme visibility - using new teals for primary actions, gold for text */
  --color-primary-600: var(--color-primary-400);    /* Lighter Teal for dark mode buttons e.g. #26a69a */
  --color-primary-500: var(--color-primary-500);    /* Base Teal for dark mode interactions e.g. #009688 */
  
  /* Enhanced form field backgrounds using existing dark gold theme, as it's already "premium" */
  --form-bg: #3a2f26;            /* Keeping existing dark gold theme form bg */
  --form-border: #CA8A04;         /* Keeping existing dark gold theme form border */
  --form-text: #FEF3C7;           /* Keeping existing dark gold theme form text */
}

/* Base styles with modern reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
  height: 100%;
  
  /* Responsive font scaling */
  @media (max-width: 640px) {
    font-size: 14px;
  }
  
  @media (min-width: 1200px) {
    font-size: 17px;
  }
}

body {
  font-family: var(--font-family-base); /* Updated with Inter */
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: var(--leading-normal);
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile */
  transition: background-color var(--transition-normal), color var(--transition-normal);
  
  /* Prevent horizontal scroll on mobile */
  overflow-x: hidden;
}

/* Typography improvements for financial interfaces */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-financial); /* Updated with Inter */
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin: 0;
}

h1 { font-size: var(--text-3xl); font-weight: var(--font-weight-bold); }
h2 { font-size: var(--text-2xl); }
h3 { font-size: var(--text-xl); }
h4 { font-size: var(--text-lg); }
h5 { font-size: var(--text-base); }
h6 { font-size: var(--text-sm); }

p {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* Financial data typography with tabular figures */
.financial-data,
.currency,
.percentage,
.price {
  font-family: var(--font-family-mono);
  font-variant-numeric: tabular-nums lining-nums;
  letter-spacing: 0.025em;
}

/* Link styles with golden theme */
a {
  color: var(--color-primary-700); /* Updated to new primary teal */
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-800); /* Darker shade of new primary teal */
    text-decoration: underline;
  }
  
  &:focus-visible {
    outline: 2px solid var(--border-focus); /* Uses new primary teal for focus */
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

/* Button base styles */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  color: inherit;
  transition: all var(--transition-fast);
  
  &:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

/* Form elements base styles */
input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--border-focus); /* Updated to new primary teal */
    box-shadow: 0 0 0 3px hsla(var(--color-primary-700)/0.1); /* Shadow to match new primary teal */
  }
  
  &:disabled {
    background-color: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.7;
  }
  
  /* Fix for invisible text issues */
  &::placeholder {
    color: var(--text-muted);
    opacity: 1;
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: var(--radius-md);
  
  &:hover {
    background: var(--color-gray-500);
  }
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) var(--bg-secondary);
}

/* Focus management for accessibility */
.focus-ring {
  &:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Accessibility improvements for motion */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-primary: #000000;
    --text-secondary: #000000;
  }
  
  [data-theme="dark"] {
    --border-primary: #ffffff;
    --border-secondary: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  .financial-data {
    font-family: monospace !important;
  }
}

/* Loading states and animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Modern glassmorphism effects for overlays */
.glass {
  background: var(--bg-overlay);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile-first responsive utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  
  @media (min-width: 640px) {
    padding: 0 var(--space-6);
  }
  
  @media (min-width: 1024px) {
    padding: 0 var(--space-8);
  }
}

/* Safe area handling for mobile devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Modern card component base */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--border-secondary);
  }
}

/* Financial status indicators with accessibility */
.status-positive {
  color: var(--color-success-600);
}

.status-negative {
  color: var(--color-warning-600);
}

.status-neutral {
  color: var(--text-secondary);
}

/* Ensure proper target sizes for touch interfaces (WCAG 2.2) */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modern gradient backgrounds for brand elements with golden theme */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%); /* New primary teal gradient */
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary-600) 0%, var(--color-secondary-700) 100%); /* New secondary blue-gray gradient */
}

/* Investra-specific gradient combinations - updated with new accent and primary */
.gradient-investra {
  background: linear-gradient(135deg, var(--color-accent-500) 0%, var(--color-accent-700) 50%, var(--color-accent-900) 100%); /* Gold accent gradient */
}

.gradient-investra-subtle {
  background: linear-gradient(135deg, var(--color-accent-50) 0%, var(--color-accent-100) 100%); /* Lighter gold accent gradient */
}
