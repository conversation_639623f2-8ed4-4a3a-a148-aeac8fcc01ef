/* Investra Golden Button Components */

.btn-investra {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-family-financial);
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  min-height: var(--input-height);
  
  /* Primary Golden Button */
  &.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
    color: white;
    box-shadow: var(--shadow-md);
    
    &:hover {
      background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
      box-shadow: var(--shadow-lg);
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }
    
    &:focus-visible {
      outline: 2px solid var(--color-primary-300);
      outline-offset: 2px;
    }
  }
  
  /* Secondary Golden Button */
  &.btn-secondary {
    background: transparent;
    color: var(--color-primary-600);
    border: 2px solid var(--color-primary-500);
    
    &:hover {
      background: var(--color-primary-50);
      border-color: var(--color-primary-600);
      color: var(--color-primary-700);
    }
    
    &:active {
      background: var(--color-primary-100);
    }
  }
  
  /* Subtle Golden Button */
  &.btn-subtle {
    background: var(--color-primary-50);
    color: var(--color-primary-700);
    border: 1px solid var(--color-primary-200);
    
    &:hover {
      background: var(--color-primary-100);
      border-color: var(--color-primary-300);
    }
  }
  
  /* Small size */
  &.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    min-height: var(--input-height-sm);
  }
  
  /* Large size */
  &.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    min-height: var(--input-height-lg);
  }
  
  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    
    &:hover {
      background: revert;
      box-shadow: revert;
    }
  }
}

/* Golden Card Component */
.card-investra {
  background: var(--bg-card);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary-300);
    transform: translateY(-2px);
  }
  
  &.card-premium {
    background: linear-gradient(135deg, var(--color-primary-50) 0%, rgba(255, 251, 235, 0.5) 100%);
    border: 2px solid var(--color-primary-300);
  }
}

/* Golden Input Fields */
.input-investra {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: var(--font-family-base);
  font-size: var(--text-base);
  background: var(--bg-card);
  border: 2px solid var(--color-primary-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  min-height: var(--input-height);
  
  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(202, 138, 4, 0.1);
    background: var(--color-primary-50);
  }
  
  &::placeholder {
    color: var(--color-primary-400);
    opacity: 0.7;
  }
}

/* Golden Badge/Tag */
.badge-investra {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-md);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  
  &.badge-primary {
    background: var(--color-primary-100);
    color: var(--color-primary-800);
    border: 1px solid var(--color-primary-300);
  }
  
  &.badge-success {
    background: var(--color-success-100);
    color: var(--color-success-800);
    border: 1px solid var(--color-success-300);
  }
  
  &.badge-warning {
    background: var(--color-warning-100);
    color: var(--color-warning-800);
    border: 1px solid var(--color-warning-300);
  }
}

/* Financial Data Display with Golden Accents */
.financial-metric {
  font-family: var(--font-family-mono);
  font-variant-numeric: tabular-nums;
  font-weight: var(--font-weight-semibold);
  
  &.metric-positive {
    color: var(--color-success-600);
  }
  
  &.metric-negative {
    color: var(--color-warning-600);
  }
  
  &.metric-neutral {
    color: var(--color-primary-600);
  }
  
  &.metric-highlight {
    background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-primary-50) 100%);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    border: 1px solid var(--color-primary-200);
  }
}

/* Premium Loading Spinner */
.spinner-investra {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-primary-200);
  border-top: 3px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
