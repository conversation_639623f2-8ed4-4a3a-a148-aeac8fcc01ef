/* Emergency CSS fix for Transactions page layout */

.enhanced-page-container {
  max-width: 1400px; /* Increased from 1200px for more overall space */
  margin: 0 auto;
  padding: var(--space-6, 24px); /* Reduced padding to give more content space */
  min-height: 100vh;
}

.enhanced-page-header {
  margin-bottom: var(--space-8, 32px); /* Increased margin */
  padding-bottom: var(--space-4, 16px); /* Kept padding or use var(--space-5) */
  border-bottom: 1px solid var(--border-primary, var(--color-gray-200));
}

.enhanced-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.enhanced-header-main {
  display: flex;
  align-items: center;
  gap: 12px;
}

.enhanced-header-icon {
  width: 24px;
  height: 24px;
  color: var(--color-primary-700, var(--color-primary-700)); /* USE NEW PRIMARY, fallback is new primary */
}

.enhanced-page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary, var(--color-gray-800)); /* UPDATED FALLBACK */
}

.enhanced-page-subtitle {
  font-size: 14px;
  color: var(--text-secondary, var(--color-gray-600)); /* UPDATED FALLBACK */
  margin: 4px 0 0 0;
}

.enhanced-content-layout {
  display: flex;
  flex-direction: column;
  gap: var(--space-5, 20px); /* Reduced from 24px for more content space */
}

.transaction-grid-row {
  display: grid;
  grid-template-columns: 0.8fr 1.2fr; /* Better balance: 40% left, 60% right */
  gap: var(--space-5, 20px); /* Reduced gap for more content space */
  align-items: start;
}

/* For larger screens, optimize the ratio further */
@media (min-width: 1200px) {
  .transaction-grid-row {
    grid-template-columns: 0.75fr 1.25fr; /* 37.5% left, 62.5% right on larger screens */
  }
}

/* For extra-wide screens, increase container width even more */
@media (min-width: 1600px) {
  .enhanced-page-container {
    max-width: 1600px; /* Even more space for very wide screens */
  }
  .transaction-grid-row {
    grid-template-columns: 0.7fr 1.3fr; /* 35% left, 65% right for ultra-wide */
  }
}

@media (max-width: 1024px) {
  .transaction-grid-row {
    grid-template-columns: 1fr; /* Single column for tablets and mobiles */
    gap: var(--space-4, 16px);
  }
}

/* Added a breakpoint for smaller tablets / larger phones if needed, but 1024px might be fine */
@media (max-width: 768px) {
  .enhanced-page-container {
    padding: var(--space-4, 16px); /* Further reduce padding for smaller screens */
  }
  .enhanced-content-layout {
    gap: var(--space-4, 16px); /* Further reduce gap */
  }
  .enhanced-page-title {
    font-size: 20px; /* Reduced from 24px */
  }
  .enhanced-page-subtitle {
    font-size: 13px; /* Reduced from 14px */
  }
  .enhanced-section-title {
    font-size: 16px; /* Reduced from 18px */
  }
  .enhanced-section-subtitle {
    font-size: 13px; /* Reduced from 14px */
  }
}

.enhanced-form-section,
.enhanced-transactions-section {
  background: var(--bg-card, #ffffff); /* Use --bg-card for consistency */
  border: 1px solid var(--border-primary, var(--color-gray-200)); /* UPDATED FALLBACK */
  border-radius: 8px;
  overflow: hidden;
}

.enhanced-section-header {
  padding: var(--space-4, 16px) var(--space-5, 20px); /* Standardized padding */
  background: var(--bg-secondary, var(--color-gray-50));
  border-bottom: 1px solid var(--border-primary, var(--color-gray-200));
}

.enhanced-section-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.enhanced-section-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary-700, var(--color-primary-700)); /* USE NEW PRIMARY, fallback is new primary */
}

.enhanced-section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary, var(--color-gray-800)); /* UPDATED FALLBACK */
}

.enhanced-section-subtitle {
  font-size: 14px;
  color: var(--text-secondary, var(--color-gray-600)); /* UPDATED FALLBACK */
  margin: 4px 0 0 0;
}

.enhanced-form-wrapper,
.enhanced-transactions-wrapper {
  padding: var(--space-6, 24px); /* Increased padding */
  display: flex;
  flex-direction: column;
  flex: 1;
}

.enhanced-error-state {
  text-align: center;
  padding: var(--space-10, 40px) var(--space-5, 20px); /* Standardized padding */
  color: var(--text-secondary, var(--color-gray-600));
}

.enhanced-loading-state { /* Added styles for loading state */
  text-align: center;
  padding: var(--space-10, 40px) var(--space-5, 20px);
  color: var(--text-secondary, var(--color-gray-600));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px; /* Ensure it takes some space */
}

.loading-spinner-large { /* Style for a potentially larger spinner */
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-primary-300, var(--color-primary-300));
  border-top-color: var(--color-primary-700, var(--color-primary-700));
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4, 16px);
}

/* Keyframes for spin animation if not already global */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-icon-wrapper {
  margin-bottom: 16px;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: var(--color-accent-700, var(--color-accent-700)); /* USE NEW ACCENT, fallback is new accent */
  margin: 0 auto;
}

.error-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--text-primary, var(--color-gray-800)); /* UPDATED FALLBACK */
}

.error-description {
  font-size: 16px;
  margin: 0;
  color: var(--text-secondary, var(--color-gray-600)); /* UPDATED FALLBACK */
}

/* Ensure transaction components fit properly */
.enhanced-form-wrapper > * {
  width: 100%;
}

.enhanced-transactions-wrapper > * {
  width: 100%;
}

/* Standardize container heights for consistent layout */
.enhanced-form-section,
.enhanced-transactions-section {
  min-height: 500px; /* Minimum height for consistency */
  display: flex;
  flex-direction: column;
}

.enhanced-form-wrapper,
.enhanced-transactions-wrapper {
  flex: 1; /* Take remaining space in the container */
  display: flex;
  flex-direction: column;
}

/* Ensure form and transaction list take available space */
.enhanced-form-wrapper .form-container,
.enhanced-transactions-wrapper > div {
  flex: 1;
  min-height: 0; /* Allow flexbox to work properly */
}
