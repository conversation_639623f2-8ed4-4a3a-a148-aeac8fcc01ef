// UI Component Library
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { Input } from './Input';
export type { InputProps } from './Input';

export { Card } from './Card';
export type { CardProps } from './Card';

export { Badge } from './Badge';
export type { BadgeProps } from './Badge';

export { Alert } from './Alert';
export type { AlertProps } from './Alert';

export { Spinner } from './Spinner';
export type { SpinnerProps } from './Spinner';

export { Modal } from './Modal';
export type { ModalProps } from './Modal';

// Layout components
export { Container, Grid, Flex, Show, Hide, Stack } from './Layout';
export { media, breakpoints } from '../../styles/breakpoints';

// Re-export commonly used icons from lucide-react for convenience
export {
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Search,
  X,
  Check,
  AlertCircle,
  Info,
  Settings,
  Plus,
  Minus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Download,
  Upload,
  RefreshCw,
  Calendar,
  Clock,
  User,
  Mail,
  Phone,
  Home,
  Building,
  MapPin,
  Star,
  Heart,
  Share,
  Copy,
  ExternalLink,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
