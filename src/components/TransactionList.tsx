import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { ArrowDownCircle, ArrowUpCircle, Gift, Edit3, Trash2, Info, Clock } from 'lucide-react'; // Removed AlertTriangle, CheckCircle2
import type { Transaction, Asset } from '../lib/database/types';
import { formatCurrency, formatDate } from '../utils/formatting';
import { parseOptionSymbol } from '../utils/assetCategorization';
import CompanyLogo from './CompanyLogo';

// Extended transaction type that includes asset information
export interface TransactionWithAsset extends Transaction {
  asset: Asset;
}

const SymbolContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const SymbolText = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--space-1, 0.25rem); /* Use CSS var */
`;

const SymbolName = styled.div`
  font-weight: var(--font-weight-semibold); /* Use CSS var */
  font-size: var(--text-sm); /* Use CSS var */
  color: var(--text-primary);
  font-family: var(--font-family-mono);
  letter-spacing: 0.5px;
  
  [data-theme="dark"] & {
    color: var(--text-primary); /* This should already pick up dark theme text-primary */
  }
`;

const OptionFullSymbol = styled.div`
  font-size: var(--text-xs); /* Smaller than main symbol */
  color: var(--text-secondary); /* Muted color */
  font-family: var(--font-family-mono);
  letter-spacing: 0.25px;
  
  [data-theme="dark"] & {
    color: var(--text-secondary);
  }
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--space-4, 1rem); /* Use CSS var */
`;

const FilterBar = styled.div`
  display: flex;
  display: flex;
  gap: var(--space-4, 1rem); /* Use CSS var */
  flex-wrap: wrap;
  margin-bottom: var(--space-6, 1.5rem); /* Use CSS var, adjusted from 2rem */
  padding: var(--space-5, 1.25rem); /* Use CSS var, adjusted from 1.5rem */
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  
  [data-theme="dark"] & {
    background: var(--bg-card); /* Correctly inherits */
    border-color: var(--border-primary); /* Correctly inherits */
  }

  @media (max-width: 480px) {
    padding: var(--space-4, 1rem);
    gap: var(--space-3, 0.75rem);
  }
`;

const FilterSelect = styled.select`
  padding: var(--space-3, 0.75rem); /* Use CSS var */
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm); /* Use CSS var */
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-width: 160px; /* Slightly wider */
  flex-grow: 1; /* Allow selects to grow */
  transition: all var(--transition-fast);
  
  @media (max-width: 480px) {
    min-width: 100%; /* Full width on small screens */
  }

  &:focus {
    outline: none;
    border-color: var(--border-focus); /* Use new focus color */
    box-shadow: 0 0 0 3px hsla(var(--color-primary-700)/0.15); /* Use new focus shadow */
  }
  
  [data-theme="dark"] & {
    background-color: var(--form-bg, var(--bg-tertiary)); /* Align with other form inputs in dark mode */
    color: var(--form-text, var(--text-primary));
    border-color: var(--form-border, var(--border-primary));
    
    &:focus {
      border-color: var(--color-primary-400); /* Lighter teal for dark focus */
      box-shadow: 0 0 0 3px hsla(var(--color-primary-400)/0.25); /* Lighter teal shadow */
    }
  }
`;

const FilterInput = styled.input`
  padding: var(--space-3, 0.75rem); /* Use CSS var */
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm); /* Use CSS var */
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-width: 180px; /* Slightly wider than selects for text input */
  flex-grow: 1; /* Allow input to grow */
  transition: all var(--transition-fast);
  
  &::placeholder {
    color: var(--text-muted);
  }
  
  @media (max-width: 480px) {
    min-width: 100%; /* Full width on small screens */
  }

  &:focus {
    outline: none;
    border-color: var(--border-focus); /* Use new focus color */
    box-shadow: 0 0 0 3px hsla(var(--color-primary-700)/0.15); /* Use new focus shadow */
  }
  
  [data-theme="dark"] & {
    background-color: var(--form-bg, var(--bg-tertiary)); /* Align with other form inputs in dark mode */
    color: var(--form-text, var(--text-primary));
    border-color: var(--form-border, var(--border-primary));
    
    &::placeholder {
      color: var(--text-muted);
    }
    
    &:focus {
      border-color: var(--color-primary-400); /* Lighter teal for dark focus */
      box-shadow: 0 0 0 3px hsla(var(--color-primary-400)/0.25); /* Lighter teal shadow */
    }
  }
`;

const TransactionTable = styled.div`
  background: var(--bg-card);
  border-radius: var(--radius-lg); /* Use CSS var */
  overflow: hidden;
  border: 1px solid var(--border-primary);
  
  [data-theme="dark"] & {
    background: var(--bg-card); /* Correctly inherits */
    border-color: var(--border-primary); /* Correctly inherits */
  }
`;

const TableHeader = styled.div`
  display: grid;
  /* Adjusted grid columns: Symbol, Type, Quantity, Price, Total, Date, Actions */
  grid-template-columns: 2.2fr 1fr 0.9fr 1fr 1fr 1.3fr 1.5fr;
  gap: var(--space-4, 1rem); /* Use CSS var */
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem); /* Use CSS vars */
  background-color: var(--bg-tertiary);
  font-weight: var(--font-weight-semibold); /* Use CSS var */
  font-size: var(--text-sm); /* Use CSS var */
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-primary);
  
  [data-theme="dark"] & {
    background-color: var(--bg-tertiary); /* Correctly inherits */
    color: var(--text-secondary); /* Correctly inherits */
    border-bottom-color: var(--border-primary); /* Correctly inherits */
  }

  @media (max-width: 768px) {
    display: none; /* Hide table header on mobile */
  }
`;

const TableRow = styled.div`
  display: grid;
  /* Adjusted grid columns to match TableHeader: Symbol, Type, Quantity, Price, Total, Date, Actions */
  grid-template-columns: 2.2fr 1fr 0.9fr 1fr 1fr 1.3fr 1.5fr;
  gap: var(--space-4, 1rem); /* Use CSS var */
  padding: var(--space-4, 1rem); /* Use CSS var */
  align-items: center; /* Vertically align items in row */
  border-bottom: 1px solid var(--border-primary);
  transition: background-color var(--transition-fast); /* Use CSS var */
  background: var(--bg-card);
  color: var(--text-primary);
  
  &:hover {
    background-color: var(--bg-secondary);
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  [data-theme="dark"] & {
    background: var(--bg-card); /* Correctly inherits */
    color: var(--text-primary); /* Correctly inherits */
    border-bottom-color: var(--border-primary); /* Correctly inherits */
    
    &:hover {
      background-color: var(--bg-secondary); /* Correctly inherits */
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr; /* Stack columns on mobile */
    gap: var(--space-3, 0.75rem);
    padding: var(--space-3, 0.75rem);

    & > div { /* Style individual cells for mobile */
      display: flex;
      justify-content: space-between;
      padding: var(--space-2, 0.5rem) 0;
      border-bottom: 1px dashed var(--border-secondary); /* Separator for cell items */

      &:last-child {
        border-bottom: none; /* No border for the action buttons container */
        justify-content: flex-start; /* Align buttons to the start */
        gap: var(--space-3);
      }

      /* Add labels using ::before pseudo-element */
      &:nth-child(1)::before { content: "Symbol: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
      &:nth-child(2)::before { content: "Type: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
      &:nth-child(3)::before { content: "Quantity: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
      &:nth-child(4)::before { content: "Price: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
      &:nth-child(5)::before { content: "Total: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
      &:nth-child(6)::before { content: "Date: "; font-weight: var(--font-weight-semibold); color: var(--text-secondary); }
    }

    /* Ensure SymbolContainer and its contents stack nicely on mobile */
    ${SymbolContainer} {
      flex-direction: row; /* Keep logo and text side-by-side */
      align-items: center;
      width: 100%; /* Take full width in its flex context */
    }
    ${SymbolText} {
      align-items: flex-start; /* Align text to the start if it wraps */
    }
     /* Adjust action buttons for mobile */
    & > div:nth-child(7) { /* Targeting the actions div */
      padding-top: var(--space-3);
    }
  }
`;

const TransactionBadge = styled.span<{ type: string }>`
  display: inline-block;
  display: inline-flex; /* To allow icon and text alignment */
  align-items: center;
  gap: var(--space-2, 0.5rem);
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem); /* Adjusted padding */
  border-radius: var(--radius-md);
  font-size: var(--text-xs); /* Use CSS var */
  font-weight: var(--font-weight-semibold); /* Use CSS var */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent; /* Base border */
  
  ${props => {
    switch (props.type) {
      case 'buy':
        return `
          background-color: var(--color-success-100);
          color: var(--color-success-700);
          border-color: var(--color-success-300);
          
          [data-theme="dark"] & {
            background-color: hsla(var(--color-success-500)/0.2);
            color: var(--color-success-300); /* Lighter green for dark */
            border-color: hsla(var(--color-success-500)/0.3);
          }
        `;
      case 'sell':
        return `
          background-color: var(--color-danger-100); /* Using danger for sell */
          color: var(--color-danger-700);
          border-color: var(--color-danger-300);
          
          [data-theme="dark"] & {
            background-color: hsla(var(--color-danger-500)/0.2);
            color: var(--color-danger-300); /* Lighter red for dark */
            border-color: hsla(var(--color-danger-500)/0.3);
          }
        `;
      case 'dividend':
        return `
          background-color: var(--color-accent-100); /* Using accent for dividend */
          color: var(--color-accent-700);
          border-color: var(--color-accent-300);
          
          [data-theme="dark"] & {
            background-color: hsla(var(--color-accent-500)/0.2);
            color: var(--color-accent-300); /* Lighter accent for dark */
            border-color: hsla(var(--color-accent-500)/0.3);
          }
        `;
      case 'option_expired':
        return `
          background-color: var(--color-warning-100); /* Using warning for expired */
          color: var(--color-warning-700);
          border-color: var(--color-warning-300);
          
          [data-theme="dark"] & {
            background-color: hsla(var(--color-warning-500)/0.2);
            color: var(--color-warning-300); /* Lighter warning for dark */
            border-color: hsla(var(--color-warning-500)/0.3);
          }
        `;
      default: // Other types like 'split'
        return `
          background-color: var(--color-gray-100);
          color: var(--color-gray-700);
          border-color: var(--color-gray-300);

          [data-theme="dark"] & {
            background-color: hsla(var(--color-gray-500)/0.2);
            color: var(--color-gray-300);
            border-color: hsla(var(--color-gray-500)/0.3);
          }
        `;
    }
  }}
`;

const AssetTypeBadge = styled.span<{ type: string }>`
  display: inline-block;
  display: inline-block;
  padding: var(--space-1, 0.25rem) var(--space-2, 0.5rem); /* Use CSS vars */
  border-radius: var(--radius-sm);
  font-size: 0.7em; /* Relative to parent, makes it smaller */
  font-weight: var(--font-weight-medium); /* Use CSS var */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: var(--space-1, 0.25rem); /* Add some space from symbol name */
  /* Default subtle appearance */
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);

  [data-theme="dark"] & {
    background-color: var(--color-gray-700); /* Darker gray for dark theme */
    color: var(--color-gray-200);
  }

  /* Specific overrides if needed, but a generic style might be better for simplicity */
  /* For example, for stock:
  ${props => props.type === 'stock' && `
    background-color: var(--color-secondary-100);
    color: var(--color-secondary-700);
    [data-theme="dark"] & {
      background-color: var(--color-secondary-700);
      color: var(--color-secondary-200);
    }
  `}
  */
`;

const ActionButton = styled.button<{ variant?: 'edit' | 'delete' }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem); /* Use CSS vars */
  border: 1px solid;
  border-radius: var(--radius-md);
  font-size: var(--text-xs); /* Use CSS var */
  font-weight: var(--font-weight-medium); /* Use CSS var */
  cursor: pointer;
  margin-right: var(--space-2, 0.5rem); /* Use CSS var */
  transition: all var(--transition-fast);
  min-width: 70px; /* Standardized minimum width */
  height: 32px; /* Standardized height */
  gap: var(--space-1, 0.25rem); /* Consistent spacing between icon and text */
  
  ${props => props.variant === 'edit' ? `
    background-color: var(--color-secondary-100); /* Lighter secondary for edit */
    color: var(--color-secondary-700);
    border-color: var(--color-secondary-300);
    
    &:hover {
      background-color: var(--color-secondary-200);
      border-color: var(--color-secondary-500);
      color: var(--color-secondary-800);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    [data-theme="dark"] & {
      background-color: var(--color-secondary-700);
      color: var(--color-secondary-100);
      border-color: var(--color-secondary-500);
      
      &:hover {
        background-color: var(--color-secondary-600);
        border-color: var(--color-secondary-300);
        color: var(--color-secondary-50);
      }
    }
  ` : ` /* Delete variant */
    background-color: var(--color-danger-100);
    color: var(--color-danger-700);
    border-color: var(--color-danger-300);
    
    &:hover {
      background-color: var(--color-danger-200);
      border-color: var(--color-danger-500);
      color: var(--color-danger-800);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    [data-theme="dark"] & {
      background-color: hsla(var(--color-danger-500)/0.2);
      color: var(--color-danger-300);
      border-color: hsla(var(--color-danger-500)/0.3);
      
      &:hover {
        background-color: hsla(var(--color-danger-500)/0.3);
        border-color: var(--color-danger-300);
        color: var(--color-danger-200);
      }
    }
  `}
`;

const EmptyState = styled.div`
  text-align: center;
  padding: var(--space-8, 2rem) var(--space-4, 1rem); /* Use CSS vars, adjusted from 3rem */
  color: var(--text-muted); /* Use CSS var */
`;

const LoadingState = styled.div`
  text-align: center;
  padding: var(--space-8, 2rem); /* Use CSS var */
  color: var(--text-muted); /* Use CSS var */
`;

const ErrorState = styled.div`
  background-color: var(--color-danger-100); /* Use CSS var */
  color: var(--color-danger-700); /* Use CSS var */
  padding: var(--space-4, 1rem); /* Use CSS var */
  border-radius: var(--radius-md); /* Use CSS var */
  margin-bottom: var(--space-4, 1rem); /* Use CSS var */
`;

interface TransactionListProps {
  transactions: TransactionWithAsset[];
  loading: boolean;
  error?: string | null;
  onEdit: (transaction: TransactionWithAsset) => void;
  onDelete: (id: string) => void;
}

const TransactionList: React.FC<TransactionListProps> = ({
  transactions,
  loading,
  error,
  onEdit,
  onDelete
}) => {
  const [filterType, setFilterType] = useState<string>('all');
  const [filterAsset, setFilterAsset] = useState<string>('all');
  const [filterSymbol, setFilterSymbol] = useState<string>('');

  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const typeMatch = filterType === 'all' || transaction.transaction_type === filterType;
      const assetMatch = filterAsset === 'all' || transaction.asset?.asset_type === filterAsset;
      const symbolMatch = !filterSymbol || 
        transaction.asset?.symbol?.toLowerCase().includes(filterSymbol.toLowerCase());
      
      return typeMatch && assetMatch && symbolMatch;
    });
  }, [transactions, filterType, filterAsset, filterSymbol]);

  const sortedTransactions = useMemo(() => {
    return [...filteredTransactions].sort((a, b) => 
      new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()
    );
  }, [filteredTransactions]);

  // Show all transactions with proper scrolling
  const recentTransactions = useMemo(() => {
    return sortedTransactions; // Show all transactions, scrolling handled by container
  }, [sortedTransactions]);

  if (loading) {
    return <LoadingState>Loading transactions...</LoadingState>;
  }

  if (error) {
    return <ErrorState>Error loading transactions: {error}</ErrorState>;
  }

  return (
    <Container>
      <FilterBar>
        <FilterSelect
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
        >
          <option value="all">All Types</option>
          <option value="buy">Buy</option>
          <option value="sell">Sell</option>
          <option value="dividend">Dividend</option>
          <option value="option_expired">Option Expired</option>
        </FilterSelect>
        
        <FilterSelect
          value={filterAsset}
          onChange={(e) => setFilterAsset(e.target.value)}
        >
          <option value="all">All Assets</option>
          <option value="stock">Stocks</option>
          <option value="etf">ETFs</option>
          <option value="option">Options</option>
          <option value="forex">Forex</option>
          <option value="crypto">Crypto</option>
          <option value="reit">REITs</option>
        </FilterSelect>
        
        <FilterInput
          type="text"
          placeholder="Filter by Symbol..."
          value={filterSymbol}
          onChange={(e) => setFilterSymbol(e.target.value)}
        />
      </FilterBar>

      {recentTransactions.length === 0 ? (
        <EmptyState>
          {transactions.length === 0 
            ? "No transactions yet. Click 'Add Transaction' to get started."
            : "No transactions match the current filters."
          }
        </EmptyState>
      ) : (
        <TransactionTable>
          <TableHeader>
            <div>Symbol</div>
            <div>Type</div>
            <div>Quantity</div>
            <div>Price</div>
            <div>Total</div>
            <div>Date</div>
            <div>Actions</div>
          </TableHeader>
          
          <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
            {recentTransactions
              .filter(transaction => transaction && transaction.id) // Filter out invalid transactions
              .map((transaction) => (
            <TableRow key={transaction.id}>
              <SymbolContainer>
                <CompanyLogo
                  symbol={(() => {
                    // For options, use the underlying symbol for the logo
                    if (transaction.asset?.asset_type === 'option') {
                      const parsedSymbol = parseOptionSymbol(transaction.asset.symbol);
                      if (parsedSymbol) {
                        return parsedSymbol.underlying.toUpperCase();
                      }
                    }
                    return transaction.asset?.symbol || 'N/A';
                  })()}
                  size="md"
                />
                <SymbolText>
                  <SymbolName>
                    {(() => {
                      if (transaction.asset?.asset_type === 'option') {
                        const parsedSymbol = parseOptionSymbol(transaction.asset.symbol);
                        if (parsedSymbol) {
                          // Show the underlying symbol as the main name
                          return parsedSymbol.underlying.toUpperCase();
                        }
                      }
                      return transaction.asset?.symbol || 'N/A';
                    })()}
                  </SymbolName>
                  {transaction.asset?.asset_type === 'option' && (
                    <OptionFullSymbol>
                      {(() => {
                        const parsedSymbol = parseOptionSymbol(transaction.asset.symbol);
                        if (parsedSymbol) {
                          // Format: "May 16 $0 CALL"
                          const month = parsedSymbol.expiration.toLocaleDateString('en-US', { month: 'short' });
                          const day = parsedSymbol.expiration.getDate();
                          const strike = parsedSymbol.strike === 0 ? '0' : parsedSymbol.strike.toString();
                          const type = parsedSymbol.type.toUpperCase();
                          return `${month} ${day} $${strike} ${type}`;
                        }
                        return transaction.asset.symbol.toLowerCase();
                      })()}
                    </OptionFullSymbol>
                  )}
                  {transaction.asset?.asset_type !== 'option' && (
                    <div
                      className="transaction-company-name"
                      title={transaction.asset?.name || 'Unknown Company'}
                    >
                      {transaction.asset?.name || 'Unknown Company'}
                    </div>
                  )}
                  <AssetTypeBadge type={transaction.asset?.asset_type || 'stock'}>
                    {transaction.asset?.asset_type || 'stock'}
                  </AssetTypeBadge>
                </SymbolText>
              </SymbolContainer>
            
              <div>
                <TransactionBadge type={transaction.transaction_type || 'buy'}>
                  {transaction.transaction_type === 'buy' && <ArrowDownCircle size="0.9em" />}
                  {transaction.transaction_type === 'sell' && <ArrowUpCircle size="0.9em" />}
                  {transaction.transaction_type === 'dividend' && <Gift size="0.9em" />}
                  {transaction.transaction_type === 'split' && <Info size="0.9em" />}
                  {transaction.transaction_type === 'option_expired' && <Clock size="0.9em" />}
                  {/* Add other icons as needed */}
                  {!['buy', 'sell', 'dividend', 'split', 'option_expired'].includes(transaction.transaction_type) && <Info size="0.9em" />}
                  <span style={{ marginLeft: '0.25em' }}>{transaction.transaction_type === 'option_expired' ? 'Option Expired' : transaction.transaction_type || 'buy'}</span>
                </TransactionBadge>
              </div>
              
              <div className="financial-data">
                {(transaction.quantity || 0).toLocaleString()}
              </div>
              
              <div className="financial-data">
                {formatCurrency(transaction.price || 0, transaction.currency || 'USD')}
              </div>
              
              <div className="financial-data">
                {formatCurrency(transaction.total_amount || 0, transaction.currency || 'USD')}
              </div>
              
              <div>
                {(() => {
                  // Handle timezone-safe date formatting for database date strings
                  const dateStr = transaction.transaction_date;
                  if (typeof dateStr === 'string') {
                    // Parse date string safely to avoid timezone shifts
                    const [year, month, day] = dateStr.split('-').map(Number);
                    const safeDate = new Date(year, month - 1, day); // month is 0-indexed
                    return formatDate(safeDate);
                  }
                  return formatDate(dateStr);
                })()}
              </div>
              
              <div>
                <ActionButton 
                  variant="edit" 
                  onClick={() => onEdit(transaction)}
                >
                  <Edit3 size="0.875em" /> Edit
                </ActionButton>
                <ActionButton 
                  variant="delete" 
                  onClick={() => onDelete(transaction.id)}
                >
                  <Trash2 size="0.875em" /> Delete
                </ActionButton>
              </div>
            </TableRow>
          ))}
          </div>
        </TransactionTable>
      )}
    </Container>
  );
};

export default TransactionList;
